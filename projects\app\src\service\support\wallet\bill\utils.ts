import { ModelTypeEnum, getModelMap } from '@/service/core/ai/model';
import { AuthUserTypeEnum } from '@wiserag/global/support/permission/constant';
import { BillSourceEnum, PRICE_SCALE } from '@wiserag/global/support/wallet/bill/constants';

export function authType2BillSource({
  authType,
  shareId,
  source
}: {
  authType?: `${AuthUserTypeEnum}`;
  shareId?: string;
  source?: `${BillSourceEnum}`;
}) {
  if (source) return source;
  if (shareId) return BillSourceEnum.shareLink;
  if (authType === AuthUserTypeEnum.apikey) return BillSourceEnum.api;
  return BillSourceEnum.wisegpt;
}

/**
 * TypeScript 中的“formatModelPrice2Store”函数根据输入和输出长度、类型和指定倍数格式化模型价格。
 *
 * @param  `formatModelPrice2Store` 函数接受一个具有以下参数的对象：
 * @return 函数“formatModelPrice2Store”返回一个具有以下属性的对象：
 * - `modelName`：从 `modelData` 检索到的模型的名称。
 * - `inputTotal`：根据输入长度、`modelData` 的输入价格和指定的 `multiple` 计算出的总输入价格。
 * - `outputTotal`：根据输出长度、输出价格计算出的总输出价格
 */
export const formatModelPrice2Store = ({
  model,
  inputLen = 0,
  outputLen = 0,
  type,
  multiple = 1000
}: {
  model: string;
  inputLen: number;
  outputLen?: number;
  type: `${ModelTypeEnum}`;
  multiple?: number;
}) => {
  const modelData = getModelMap?.[type]?.(model);
  if (!modelData)
    return {
      inputTotal: 0,
      outputTotal: 0,
      total: 0,
      modelName: ''
    };
  const inputTotal = modelData.inputPrice
    ? Math.ceil(modelData.inputPrice * (inputLen / multiple) * PRICE_SCALE)
    : 0;
  const outputTotal = modelData.outputPrice
    ? Math.ceil(modelData.outputPrice * (outputLen / multiple) * PRICE_SCALE)
    : 0;

  return {
    modelName: modelData.name,
    inputTotal: inputTotal,
    outputTotal: outputTotal,
    total: inputTotal + outputTotal
  };
};
