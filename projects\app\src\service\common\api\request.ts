import { SERVICE_LOCAL_HOST } from '@wiserag/service/common/system/tools';
import axios, { Method, InternalAxiosRequestConfig, AxiosResponse } from 'axios';

interface ConfigType {
  headers?: { [key: string]: string };
  hold?: boolean;
  timeout?: number;
}
interface ResponseDataType {
  code: number;
  message: string;
  data: any;
}

/**
 * 请求开始
 */
function requestStart(config: InternalAxiosRequestConfig): InternalAxiosRequestConfig {
  return config;
}

/**
 * 请求成功,检查请求头
 */
function responseSuccess(response: AxiosResponse<ResponseDataType>) {
  return response;
}
/**
 * 响应数据检查
 */
function checkRes(data: ResponseDataType) {
  if (data === undefined) {
    console.log('error->', data, 'data is empty');
    return Promise.reject('服务器异常');
  } else if (data?.code && (data.code < 200 || data.code >= 400)) {
    return Promise.reject(data);
  }
  return data.data;
}

/**
 * 响应错误
 */
function responseError(err: any) {
  if (!err) {
    return Promise.reject({ message: '未知错误' });
  }
  if (typeof err === 'string') {
    return Promise.reject({ message: err });
  }

  if (err?.response?.data) {
    return Promise.reject(err?.response?.data);
  }
  return Promise.reject(err);
}

/* 创建请求实例 */
const instance = axios.create({
  timeout: 60000, // 超时时间
  headers: {
    'content-type': 'application/json',
    'Cache-Control': 'no-cache'
  }
});

/* 请求拦截 */
instance.interceptors.request.use(requestStart, (err) => Promise.reject(err));
/* 响应拦截 */
instance.interceptors.response.use(responseSuccess, (err) => Promise.reject(err));

/**
 * 函数“request”发送带有数据和配置选项的 HTTP 请求，在发出请求之前处理数据中的 null 或未定义值。
 *
 * @param url “request”函数中的“url”参数是一个字符串，表示请求将发送到的端点或 URL。它指定客户端想要与之交互的服务器上的资源的位置。
 * @param data request函数中的data参数用于传递请求需要发送的数据。它可以是一个对象，其中包含要在 POST 和 PUT 等方法的请求正文中发送的数据键值对，也可以作为 GET 和
 * PUT 等方法的查询参数。
 * @param config “request”函数中的“config”参数的类型为“ConfigType”。该参数用于为 HTTP
 * 请求提供自定义配置选项，例如标头、超时设置、身份验证凭据等。它允许“request”函数的调用者自定义 HTTP 请求的行为
 * @param method
 * request函数中的method参数用于指定请求的HTTP方法。它可以是以下值之一：“GET”、“POST”、“PUT”、“DELETE”等。这决定了请求如何与服务器交互。
 * @return “request”函数返回一个 Promise，该 Promise 使用“instance”以及提供的 URL、数据、配置和方法发出请求。该函数处理从数据对象中删除任何 null
 * 或未定义的值，根据方法类型（数据的 POST 或 PUT，其他参数）构造请求，并包含提供的任何自定义配置。然后使用“check”检查请求的响应
 */
export function request(url: string, data: any, config: ConfigType, method: Method): any {
  /* 去空 */
  for (const key in data) {
    if (data[key] === null || data[key] === undefined) {
      delete data[key];
    }
  }

  return instance
    .request({
      baseURL: `http://${SERVICE_LOCAL_HOST}`,
      url,
      method,
      data: ['POST', 'PUT'].includes(method) ? data : null,
      params: !['POST', 'PUT'].includes(method) ? data : null,
      ...config // custom config
    })
    .then((res) => checkRes(res.data))
    .catch((err) => responseError(err));
}

/**
 * api请求方式
 * @param {String} url
 * @param {Any} params
 * @param {Object} config
 * @returns
 */
export function GET<T = undefined>(url: string, params = {}, config: ConfigType = {}): Promise<T> {
  return request(url, params, config, 'GET');
}

export function POST<T = undefined>(url: string, data = {}, config: ConfigType = {}): Promise<T> {
  return request(url, data, config, 'POST');
}

export function PUT<T = undefined>(url: string, data = {}, config: ConfigType = {}): Promise<T> {
  return request(url, data, config, 'PUT');
}

export function DELETE<T = undefined>(url: string, data = {}, config: ConfigType = {}): Promise<T> {
  return request(url, data, config, 'DELETE');
}
