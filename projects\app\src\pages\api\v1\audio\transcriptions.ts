import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { authCert } from '@wiserag/service/support/permission/auth/common';
import { withNextCors } from '@wiserag/service/common/middle/cors';
import { getUploadModel } from '@wiserag/service/common/file/multer';
import { removeFilesByPaths } from '@wiserag/service/common/file/utils';
import fs from 'fs';
import { getAIApi } from '@wiserag/service/core/ai/config';
import { pushWhisperBill } from '@/service/support/wallet/bill/push';
import axios from 'axios';

const upload = getUploadModel({
  maxSize: 2
});

export default withNextCors(async function handler(req: NextApiRequest, res: NextApiResponse<any>) {
  let filePaths: string[] = [];

  // '======进行语音识别=========='

  try {
    const {
      file,
      data: { duration }
    } = await upload.doUpload<{ duration: number; shareId?: string }>(req, res);

    filePaths = [file.path];

    const { teamId, tmbId } = await authCert({ req, authToken: true });

    if (!global.whisperModel) {
      throw new Error('whisper model not found');
    }

    if (!file) {
      throw new Error('file not found');
    }
    // 外部增加一个语音的在线调用参数配置或者统一使用接入的oneAPI 再或者替换getAIApi服务为获取特定的语音识别接口地址

    // 使用本地起的直接服务，符合openai规范或使用openai在线服务(包括代理)
    // const ai = getAIApi({userKey:{key:'sk-i7bbkueyGQ9QwfHB2aD16161C2634702B4A50d3f070099De',baseUrl:'http://***********:8001/v1'},timeout:3000});
    // 原生使用系统配置可以是openai在线，也可以是oneapi
    const ai = getAIApi();

    // 使用自己基于whisper官方结合fastapi起的服务(符合openai调用whisper的接口规范和请求方式)
    const FormData = require('form-data');
    function sendData(url: string, file_data: fs.ReadStream, model: string) {
      // 创建一个 FormData 实例
      const form = new FormData();

      // 添加模型名称，你可以根据实际情况修改这个值
      form.append('model', model);

      // 添加文件，假设 `filePath` 是你的音频文件的路径
      form.append('file', file_data);

      // 添加语言，根据需要设置
      form.append('language', 'Chinese');

      // 使用 axios 发送 POST 请求
      axios
        .post(url, form, {
          headers: {
            // FormData 会自动设置 Content-Type 为 multipart/form-data，并且附带 boundary 参数
            'Content-Type': 'multipart/form-data',
            // 设置 Authorization 头，这里假设你已经设置了环境变量 `OPENAI_API_KEY`
            Authorization: `Bearer ${process.env.OPENAI_API_KEY}`
          }
        })
        .then((response) => {
          // 请求成功，处理返回的数据
          console.log(response.data);
        })
        .catch((error) => {
          // 请求发生错误，进行错误处理
          console.error(error);
        });
    }

    // sendData('http://***********:5000/v1/audio/transcriptions',fs.createReadStream(file.path),global.whisperModel.model)

    // 语音识别结果
    const result = await ai.audio.transcriptions.create({
      file: fs.createReadStream(file.path),
      model: global.whisperModel.model
    });

    pushWhisperBill({
      teamId,
      tmbId,
      duration
    });
    jsonRes(res, {
      data: result.text
    });
  } catch (err) {
    console.log(err);
    jsonRes(res, {
      code: 500,
      error: err
    });
  }

  removeFilesByPaths(filePaths);
});

export const config = {
  api: {
    bodyParser: false
  }
};
