import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { connectToDatabase } from '@/service/mongo';
import type { DatasetDataListItemType } from '@/global/core/dataset/type.d';
import type { GetDatasetDataListProps } from '@/global/core/api/datasetReq';
import {
  authDatasetCollection,
  authDatasetCollectionNew
} from '@wiserag/service/support/permission/auth/dataset';
import { MongoDatasetData } from '@wiserag/service/core/dataset/data/schema';
import { PagingData } from '@/types';
import { MongoDatasetCollection } from '@wiserag/service/core/dataset/collection/schema';

export default async function handler(req: NextApiRequest, res: NextApiResponse<any>) {
  try {
    await connectToDatabase();
    let {
      pageNum = 1,
      pageSize = 10,
      searchText = '',
      collectionId
    } = req.body as GetDatasetDataListProps;

    pageSize = Math.min(pageSize, 30);

    // 凭证校验
    //  const { teamId, collection } = await authDatasetCollection({
    //   req,
    //   authToken: true,
    //   authApiKey: true,
    //   collectionId,
    //   per: 'r'
    // });

    // change
    const teamId = await MongoDatasetCollection.findOne({ _id: collectionId });
    const { collection } = await authDatasetCollectionNew({
      req,
      authToken: true,
      authApiKey: true,
      collectionId,
      teamId: String(teamId?.teamId),
      per: 'r'
    });
    searchText = searchText.replace(/'/g, '');

    const match = {
      teamId: String(teamId?.teamId), //  change
      datasetId: collection.datasetId._id,
      collectionId,
      ...(searchText
        ? {
            $or: [{ q: new RegExp(searchText, 'i') }, { a: new RegExp(searchText, 'i') }]
          }
        : {})
    };

    const [data, total] = await Promise.all([
      MongoDatasetData.find(match, '_id datasetId collectionId q a chunkIndex')
        .sort({ chunkIndex: 1, updateTime: -1 })
        .skip((pageNum - 1) * pageSize)
        .limit(pageSize)
        .lean(),
      MongoDatasetData.countDocuments(match)
    ]);

    jsonRes<PagingData<DatasetDataListItemType>>(res, {
      data: {
        pageNum,
        pageSize,
        data,
        total
      }
    });
  } catch (err) {
    jsonRes(res, {
      code: 500,
      error: err
    });
  }
}
