import type { ChatItemType } from '@wiserag/global/core/chat/type.d';
import { MongoApp } from '@wiserag/service/core/app/schema';
import { ChatSourceEnum } from '@wiserag/global/core/chat/constants';
import { MongoChatItem } from '@wiserag/service/core/chat/chatItemSchema';
import { MongoChat } from '@wiserag/service/core/chat/chatSchema';
import { addLog } from '@wiserag/service/common/system/log';
import { chatContentReplaceBlock } from '@wiserag/global/core/chat/utils';

type Props = {
  chatId: string;
  appId: string;
  teamId: string;
  userId: string;
  tmbId: string;
  variables?: Record<string, any>;
  updateUseTime: boolean;
  source: `${ChatSourceEnum}`;
  shareId?: string;
  outLinkUid?: string;
  content: [ChatItemType, ChatItemType];
  metadata?: Record<string, any>;
};

export async function saveChat({
  chatId,
  appId,
  userId,
  teamId,
  tmbId,
  variables,
  updateUseTime,
  source,
  shareId,
  outLinkUid,
  content,
  metadata = {}
}: Props) {
  try {
    const chat = await MongoChat.findOne(
      {
        appId,
        chatId
      },
      '_id metadata'
    );

    const metadataUpdate = {
      ...chat?.metadata,
      ...metadata
    };

    const promise: any[] = [
      MongoChatItem.insertMany(
        content.map((item) => ({
          chatId,
          teamId,
          tmbId,
          appId,
          ...item
        }))
      )
    ];

    const title =
      chatContentReplaceBlock(content[0].value).slice(0, 20) ||
      content[1]?.value?.slice(0, 20) ||
      'Chat';

    if (chat) {
      promise.push(
        MongoChat.updateOne(
          { appId, chatId },
          {
            title,
            updateTime: new Date(),
            metadata: metadataUpdate
          }
        )
      );
    } else {
      promise.push(
        MongoChat.create({
          userId,
          chatId,
          teamId,
          tmbId,
          appId,
          variables,
          title,
          source,
          shareId,
          outLinkUid,
          metadata: metadataUpdate
        })
      );
    }

    if (updateUseTime && source === ChatSourceEnum.online) {
      promise.push(
        MongoApp.findByIdAndUpdate(appId, {
          updateTime: new Date()
        })
      );
    }

    await Promise.all(promise);
  } catch (error) {
    addLog.error(`update chat history error`, error);
  }
}
