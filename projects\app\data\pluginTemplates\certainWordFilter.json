{"author": "RoarData Team", "templateType": "tools", "name": "合规检测", "avatar": "/imgs/module/certainWord.jpg", "intro": "可对固定或传入的文本进行合规检测并输出。", "showStatus": false, "modules": [{"moduleId": "ranvlszlnpg4", "name": "定义插件输入", "avatar": "/imgs/module/input.png", "flowType": "pluginInput", "showStatus": false, "position": {"x": 620.2965111989572, "y": -179.58002117683498}, "inputs": [{"key": "url", "valueType": "string", "label": "url", "type": "input", "required": true, "description": "", "edit": true, "editField": {"key": true, "name": true, "description": true, "required": true, "dataType": true, "inputType": true}, "connected": true}, {"key": "requestMethod", "valueType": "string", "label": "请求方法", "type": "input", "required": true, "description": "", "edit": true, "editField": {"key": true, "name": true, "description": true, "required": true, "dataType": true, "inputType": true}, "connected": true}, {"key": "input", "valueType": "string", "label": "用户输入", "type": "target", "required": true, "description": "", "edit": true, "editField": {"key": true, "name": true, "description": true, "required": true, "dataType": true, "inputType": true}, "connected": true}], "outputs": [{"key": "url", "valueType": "string", "label": "url", "type": "source", "edit": true, "targets": [{"moduleId": "sxuq6o", "key": "system_httpReqUrl"}]}, {"key": "requestMethod", "valueType": "string", "label": "请求方法", "type": "source", "edit": true, "targets": [{"moduleId": "sxuq6o", "key": "system_httpMethod"}]}, {"key": "input", "valueType": "string", "label": "用户输入", "type": "source", "edit": true, "targets": [{"moduleId": "sxuq6o", "key": "input"}]}]}, {"moduleId": "d0ntqdoj5gch", "name": "定义插件输出", "avatar": "/imgs/module/output.png", "flowType": "pluginOutput", "showStatus": false, "position": {"x": 2299.0952040040597, "y": -12.244973184586627}, "inputs": [{"key": "True", "valueType": "boolean", "label": "True", "type": "target", "required": true, "description": "", "edit": true, "editField": {"key": true, "name": true, "description": true, "required": false, "dataType": true, "inputType": false}, "connected": true}, {"key": "False", "valueType": "boolean", "label": "False", "type": "target", "required": true, "description": "", "edit": true, "editField": {"key": true, "name": true, "description": true, "required": false, "dataType": true, "inputType": false}, "connected": true}], "outputs": [{"key": "True", "valueType": "boolean", "label": "True", "type": "source", "edit": true, "targets": []}, {"key": "False", "valueType": "boolean", "label": "False", "type": "source", "edit": true, "targets": []}]}, {"moduleId": "sxuq6o", "name": "core.module.template.Httpv2 request", "avatar": "/imgs/module/http.png", "flowType": "httpRequest468WiseWeb", "showStatus": true, "position": {"x": 1077.6751622619981, "y": -461.94314054815595}, "inputs": [{"key": "switch", "type": "target", "label": "core.module.input.label.switch", "description": "core.module.input.description.Trigger", "valueType": "any", "showTargetInApp": true, "showTargetInPlugin": true, "connected": false}, {"key": "system_httpMethod", "valueType": "string", "label": "请求方法", "type": "target", "value": "POST", "required": true, "description": "", "edit": true, "editField": {"key": true, "name": true, "description": true, "required": true, "dataType": true, "inputType": true}, "connected": true}, {"key": "system_httpReqUrl", "valueType": "string", "label": "URL", "type": "target", "required": true, "description": "core.module.input.description.Http Request Url", "edit": true, "editField": {"key": true, "name": true, "description": true, "required": true, "dataType": true, "inputType": true}, "connected": true}, {"key": "system_httpHeader", "type": "custom", "valueType": "any", "value": [{"key": "Content-Type", "type": "string", "value": "application/json"}], "label": "", "description": "core.module.input.description.Http Request Header", "placeholder": "core.module.input.description.Http Request Header", "required": false, "showTargetInApp": false, "showTargetInPlugin": false, "connected": false}, {"key": "system_httpParams", "type": "hidden", "valueType": "any", "value": [], "label": "", "required": false, "showTargetInApp": false, "showTargetInPlugin": false, "connected": false}, {"key": "system_httpJsonBody", "type": "hidden", "valueType": "any", "value": "{\"content\":\"{{input}}\"}", "label": "", "required": false, "showTargetInApp": false, "showTargetInPlugin": false, "connected": false}, {"key": "DYNAMIC_INPUT_KEY", "type": "target", "valueType": "any", "label": "core.module.inputType.dynamicTargetInput", "description": "core.module.input.description.dynamic input", "required": false, "showTargetInApp": false, "showTargetInPlugin": true, "hideInApp": true, "connected": false}, {"key": "input", "valueType": "string", "label": "input", "type": "target", "required": true, "description": "", "edit": true, "editField": {"key": true, "name": true, "description": true, "required": true, "dataType": true}, "connected": true}, {"key": "system_addInputParam", "type": "addInputParam", "valueType": "any", "label": "", "required": false, "showTargetInApp": false, "showTargetInPlugin": false, "editField": {"key": true, "name": true, "description": true, "required": true, "dataType": true}, "defaultEditField": {"label": "", "key": "", "description": "", "inputType": "target", "valueType": "string", "required": true}, "connected": false}], "outputs": [{"key": "finish", "label": "core.module.output.label.running done", "description": "core.module.output.description.running done", "valueType": "boolean", "type": "source", "targets": [{"moduleId": "s3emsk", "key": "switch"}]}, {"key": "system_addOutputParam", "type": "addOutputParam", "valueType": "any", "label": "", "targets": [], "editField": {"key": true, "name": true, "description": true, "dataType": true}, "defaultEditField": {"label": "", "key": "", "description": "", "outputType": "source", "valueType": "string"}}, {"type": "source", "valueType": "boolean", "key": "message", "label": "output", "description": "", "edit": true, "editField": {"key": true, "name": true, "description": true, "dataType": true}, "targets": [{"moduleId": "s3emsk", "key": "input"}]}]}, {"moduleId": "s3emsk", "name": "core.module.template.TFSwitch", "avatar": "/imgs/module/tfSwitch.svg", "flowType": "pluginModule", "showStatus": false, "position": {"x": 1712.442480345937, "y": -226.47315148423954}, "inputs": [{"key": "pluginId", "type": "hidden", "label": "", "value": "community-tfSwitch", "valueType": "string", "connected": false, "showTargetInApp": false, "showTargetInPlugin": false}, {"key": "switch", "type": "target", "label": "core.module.input.label.switch", "description": "core.module.input.description.Trigger", "valueType": "any", "showTargetInApp": true, "showTargetInPlugin": true, "connected": true}, {"key": "input", "valueType": "any", "type": "target", "label": "core.module.input.label.TFSwitch input tip", "required": true, "edit": false, "connected": true}, {"key": "rule", "valueType": "string", "label": "core.module.input.label.TFSwitch textarea", "type": "textarea", "required": false, "description": "core.module.input.description.TFSwitch textarea", "edit": false, "editField": {"key": true, "name": true, "description": true, "required": true, "dataType": true, "inputType": true}, "connected": false, "placeholder": "core.module.input.description.TFSwitch textarea", "value": ""}], "outputs": [{"key": "true", "valueType": "boolean", "label": "True", "type": "source", "edit": false, "targets": [{"moduleId": "d0ntqdoj5gch", "key": "True"}]}, {"key": "false", "valueType": "boolean", "label": "False", "type": "source", "edit": false, "targets": [{"moduleId": "d0ntqdoj5gch", "key": "False"}]}]}]}