{"name": "@wiserag/web", "version": "1.0.0", "dependencies": {"@chakra-ui/anatomy": "^2.2.1", "@chakra-ui/icons": "^2.1.1", "@chakra-ui/next-js": "^2.1.5", "@chakra-ui/react": "^2.8.1", "@chakra-ui/styled-system": "^2.9.1", "@chakra-ui/system": "^2.6.1", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@wiserag/global": "workspace:*", "@fingerprintjs/fingerprintjs": "^4.2.1", "@monaco-editor/react": "^4.6.0", "mammoth": "^1.6.0", "i18next": "^22.5.1", "joplin-turndown-plugin-gfm": "^1.0.12", "next-i18next": "^13.3.0", "pdfjs-dist": "4.0.269", "react": "18.2.0", "react-dom": "18.2.0", "react-i18next": "^12.3.1", "turndown": "^7.1.2", "lexical": "0.12.6", "@lexical/react": "0.12.6", "papaparse": "^5.4.1", "@lexical/utils": "0.12.6", "@lexical/text": "0.12.6"}, "devDependencies": {"@types/react": "18.2.0", "@types/papaparse": "^5.3.7", "@types/react-dom": "18.2.0", "@types/turndown": "^5.0.4"}}