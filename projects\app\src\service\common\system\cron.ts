import { initSystemConfig } from '@/pages/api/common/system/getInitData';
import { generateQA } from '@/service/events/generateQA';
import { generateVector } from '@/service/events/generateVector';
import { setCron } from '@wiserag/service/common/system/cron';

/**
 * `startCron` 函数设置两个 cron 作业，用于更新系统配置和管理训练队列。
 */
export const startCron = () => {
  setUpdateSystemConfigCron();
  setTrainingQueueCron();
};

/* `setUpdateSystemConfigCron` 函数设置一个 cron 作业每 5 分钟运行一次。此 cron
作业将调用“initSystemConfig”函数来更新系统配置并记录一条消息“刷新系统配置”。 */
export const setUpdateSystemConfigCron = () => {
  setCron('*/5 * * * *', () => {
    initSystemConfig();
  });
};

/**
 * 函数“setTrainingQueueCron”设置一个 cron 作业，每分钟运行“generateVector”和“generateQA”。
 */
export const setTrainingQueueCron = () => {
  setCron('*/1 * * * *', () => {
    generateVector();
    generateQA();
  });
};
