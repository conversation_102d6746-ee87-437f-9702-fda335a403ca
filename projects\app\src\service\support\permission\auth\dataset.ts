import { DatasetDataItemType } from '@wiserag/global/core/dataset/type';
import { MongoDatasetData } from '@wiserag/service/core/dataset/data/schema';
import {
  authDatasetCollection,
  authDatasetCollectionNew
} from '@wiserag/service/support/permission/auth/dataset';
import { AuthModeType } from '@wiserag/service/support/permission/type';

/* data permission same of collection */
export async function authDatasetData({
  dataId,
  ...props
}: AuthModeType & {
  dataId: string;
}) {
  // get pg data
  const datasetData = await MongoDatasetData.findById(dataId);

  if (!datasetData) {
    return Promise.reject('core.dataset.error.Data not found');
  }

  // const result = await authDatasetCollection({
  //   ...props,
  //   collectionId: datasetData.collectionId
  // });
  const result = await authDatasetCollectionNew({
    ...props,
    collectionId: datasetData.collectionId,
    teamId: String(datasetData.teamId)
  });

  const data: DatasetDataItemType = {
    id: String(datasetData._id),
    q: datasetData.q,
    a: datasetData.a,
    chunkIndex: datasetData.chunkIndex,
    indexes: datasetData.indexes,
    datasetId: String(datasetData.datasetId),
    collectionId: String(datasetData.collectionId),
    sourceName: result.collection.name || '',
    sourceId: result.collection?.fileId || result.collection?.rawLink,
    isOwner: String(datasetData.tmbId) === result.tmbId,
    canWrite: result.canWrite
  };

  return {
    ...result,
    datasetData: data
  };
}
