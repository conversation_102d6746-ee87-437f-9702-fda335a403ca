import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { authCert } from '@wiserag/service/support/permission/auth/common';
import { MongoUser } from '@wiserag/service/support/user/schema';
import { connectToDatabase } from '@/service/mongo';

import { ERROR_ENUM } from '@wiserag/global/common/error/errorCode';
import { TeamMemberSchema, TeamMemberWithUserSchema } from '@wiserag/global/support/user/team/type';
import {
  TeamMemberRoleEnum,
  TeamMemberStatusEnum,
  notLeaveStatus
} from '@wiserag/global/support/user/team/constant';
import { MongoTeamMember } from '@wiserag/service/support/user/team/teamMemberSchema';
import { Types } from '@wiserag/service/common/mongo';

//查询团队列表
//根据userName模糊查询
export async function getTmbWithUserInfoByuserName({ userName }: { userName: any }) {
  const data = await MongoUser.find({ username: { $regex: new RegExp(userName, 'i') } });
  return data.map((user) => ({
    _id: String(user._id),
    username: user.username,
    avatar: user.avatar,
    balance: user.balance,
    timezone: user.timezone,
    visualName: user.visualName
  }));
}
export async function createTeamMember({
  userId,
  teamId,
  userName,
  role = TeamMemberRoleEnum.admin
}: {
  userId: string;
  teamId?: string;
  userName?: string;
  role: TeamMemberRoleEnum;
}) {
  // judeg team member isExists
  const tmb = await MongoTeamMember.findOne({
    userId: new Types.ObjectId(userId),
    teamId: new Types.ObjectId(teamId),
    defaultTeam: false
  });
  if (!tmb) {
    // create
    await MongoTeamMember.create({
      teamId,
      userId,
      name: userName,
      role: role,
      status: TeamMemberStatusEnum.active,
      createTime: new Date(),
      defaultTeam: false
    });
    return true;
  } else {
    return false;
  }
}
export default async function handler(req: NextApiRequest, res: NextApiResponse<any>) {
  try {
    await connectToDatabase();
    // const { teamId } = req.body as { teamId: string;};
    // const { userId } = await authCert({ req, authToken: true });
    // const { userId,teamId,username,role } = req.body as { userId: any; teamId: any;username: string;role: TeamMemberRoleEnum; };
    const { teamId, userName, role } = req.body as {
      teamId: any;
      userName: string;
      role: TeamMemberRoleEnum;
    };
    const user = await getTmbWithUserInfoByuserName({ userName });
    const userId = user[0]._id;
    const TeamMemberNew = createTeamMember({ userId, teamId, userName, role });

    jsonRes(res, {
      data: TeamMemberNew
    });
  } catch (err) {
    jsonRes(res, {
      code: 500,
      error: err
    });
  }
}
