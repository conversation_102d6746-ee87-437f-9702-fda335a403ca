import React from 'react';
import { <PERSON>lex, Box, IconButton, Menu, MenuButton, MenuList, MenuItem } from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { useQuery } from '@tanstack/react-query';
import { useTranslation } from 'next-i18next';
import MyIcon from '@wiserag/web/components/common/Icon';
import Avatar from '@/components/Avatar';
import { useAppStore } from '@/web/core/app/store/useAppStore';
import { putChatHistoryLeft } from '@/web/core/chat/api';

const SliderApps = ({ appId }: { appId: string }) => {
  const { t } = useTranslation();
  const router = useRouter();
  const { myApps, loadMyApps } = useAppStore();

  useQuery(['loadModels'], () => loadMyApps(false));

  return (
    <Flex flexDirection={'column'} h={'100%'}>
      <Box px={5} py={4}>
        <Flex
          alignItems={'center'}
          cursor={'pointer'}
          py={2}
          px={3}
          borderRadius={'md'}
          _hover={{ bg: 'myGray.200' }}
          onClick={() => router.push('/app/list')}
        >
          <IconButton
            mr={3}
            icon={<MyIcon name={'common/backFill'} w={'18px'} color={'primary.500'} />}
            bg={'white'}
            boxShadow={'1px 1px 9px rgba(0,0,0,0.15)'}
            size={'smSquare'}
            borderRadius={'50%'}
            aria-label={''}
          />
          {t('core.chat.Exit Chat')}
        </Flex>
      </Box>
      <Box flex={'1 0 0'} h={0} px={5} overflow={'overlay'}>
        {myApps.map((item) => (
          <Flex
            key={item._id}
            py={2}
            px={3}
            mb={3}
            cursor={'pointer'}
            borderRadius={'md'}
            alignItems={'center'}
            _hover={{
              bg: 'myGray.100',
              '& .more': {
                display: 'block'
              }
            }}
            {...(item._id === appId
              ? {
                  bg: 'white',
                  boxShadow: 'md'
                }
              : {
                  _hover: {
                    bg: 'myGray.200'
                  },
                  onClick: () => {
                    router.replace({
                      query: {
                        appId: item._id
                      }
                    });
                  }
                })}
          >
            <Avatar src={item.avatar} w={'24px'} />
            <Box ml={2} className={'textEllipsis'}>
              {item.name}
            </Box>
            {!!item._id && (
              <Box className="more" display={['block', 'none']}>
                <Menu autoSelect={false} isLazy offset={[0, 5]}>
                  <MenuButton
                    _hover={{ bg: 'white' }}
                    cursor={'pointer'}
                    borderRadius={'md'}
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                  >
                    <MyIcon name={'more'} w={'14px'} p={1} />
                  </MenuButton>
                  <MenuList color={'myGray.700'} minW={`90px !important`}>
                    <MenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        putChatHistoryLeft({
                          appId: item._id,
                          top: !item.top
                        });
                      }}
                    >
                      <MyIcon mr={2} name={'core/chat/setTopLight'} w={'16px'}></MyIcon>
                      {/* {item.top ? t('core.chat.Unpin') : t('core.chat.Pin')} */}
                      {item.top}
                    </MenuItem>
                  </MenuList>
                </Menu>
              </Box>
            )}
          </Flex>
        ))}
      </Box>
    </Flex>
  );
};

export default SliderApps;
