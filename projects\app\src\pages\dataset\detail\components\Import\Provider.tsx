import React, { useContext, useCallback, createContext, useState, useMemo, useEffect } from 'react';

import { formatModelPrice2Read } from '@wiserag/global/support/wallet/bill/tools';
import { splitText2Chunks } from '@wiserag/global/common/string/textSplitter';
import { TrainingModeEnum } from '@wiserag/global/core/dataset/constants';
import { useTranslation } from 'next-i18next';
import { DatasetItemType } from '@wiserag/global/core/dataset/type';
import { Prompt_AgentQA } from '@/global/core/prompt/agent';
import { UseFormReturn, useForm } from 'react-hook-form';
import { ImportProcessWayEnum } from '@/web/core/dataset/constants';
import { ImportSourceItemType } from '@/web/core/dataset/type';

type ChunkSizeFieldType = 'embeddingChunkSize';
export type FormType = {
  mode: `${TrainingModeEnum}`;
  way: `${ImportProcessWayEnum}`;
  embeddingChunkSize: number;
  customSplitChar: string;
  qaPrompt: string;
  webSelector: string;
};

type useImportStoreType = {
  parentId?: string;
  processParamsForm: UseFormReturn<FormType, any>;
  chunkSizeField?: ChunkSizeFieldType;
  maxChunkSize: number;
  minChunkSize: number;
  showChunkInput: boolean;
  showPromptInput: boolean;
  sources: ImportSourceItemType[];
  setSources: React.Dispatch<React.SetStateAction<ImportSourceItemType[]>>;
  showRePreview: boolean;
  totalChunkChars: number;
  totalChunks: number;
  chunkSize: number;
  predictPrice: number;
  priceTip: string;
  uploadRate: number;
  splitSources2Chunks: () => void;
};
const StateContext = createContext<useImportStoreType>({
  processParamsForm: {} as any,
  sources: [],
  setSources: function (value: React.SetStateAction<ImportSourceItemType[]>): void {
    throw new Error('Function not implemented.');
  },
  maxChunkSize: 0,
  minChunkSize: 0,
  showChunkInput: false,
  showPromptInput: false,
  chunkSizeField: 'embeddingChunkSize',
  showRePreview: false,
  totalChunkChars: 0,
  totalChunks: 0,
  chunkSize: 0,
  predictPrice: 0,
  priceTip: '',
  uploadRate: 50,
  splitSources2Chunks: () => {}
});

export const useImportStore = () => useContext(StateContext);

const Provider = ({
  dataset,
  parentId,
  children
}: {
  dataset: DatasetItemType;
  parentId?: string;
  children: React.ReactNode;
}) => {
  const vectorModel = dataset.vectorModel;
  const agentModel = dataset.agentModel;

  const processParamsForm = useForm<FormType>({
    defaultValues: {
      mode: TrainingModeEnum.chunk,
      way: ImportProcessWayEnum.auto,
      embeddingChunkSize: vectorModel?.defaultToken || 512,
      customSplitChar: '',
      qaPrompt: Prompt_AgentQA.description,
      webSelector: ''
    }
  });

  const { t } = useTranslation();
  const [sources, setSources] = useState<ImportSourceItemType[]>([]);
  const [showRePreview, setShowRePreview] = useState(false);

  // watch form
  const mode = processParamsForm.watch('mode');
  const way = processParamsForm.watch('way');
  const embeddingChunkSize = processParamsForm.watch('embeddingChunkSize');
  const customSplitChar = processParamsForm.watch('customSplitChar');

  // 控制文件上传拆分基础参数
  const modeStaticParams = {
    [TrainingModeEnum.chunk]: {
      chunkSizeField: 'embeddingChunkSize' as ChunkSizeFieldType,
      chunkOverlapRatio: 0.2,
      maxChunkSize: vectorModel?.maxToken || 512,
      minChunkSize: 100,
      autoChunkSize: vectorModel?.defaultToken || 512,
      chunkSize: embeddingChunkSize,
      showChunkInput: true,
      showPromptInput: false,
      inputPrice: vectorModel.inputPrice,
      outputPrice: 0,
      priceTip: t('core.dataset.import.Embedding Estimated Price Tips', {
        price: vectorModel.inputPrice
      }),
      uploadRate: 150
    },
    [TrainingModeEnum.qa]: {
      chunkOverlapRatio: 0,
      maxChunkSize: 8000,
      minChunkSize: 3000,
      autoChunkSize: agentModel.maxContext * 0.55 || 6000,
      chunkSize: agentModel.maxContext * 0.55 || 6000,
      showChunkInput: false,
      showPromptInput: true,
      inputPrice: agentModel.inputPrice,
      outputPrice: agentModel.outputPrice,
      priceTip: t('core.dataset.import.QA Estimated Price Tips', {
        price: agentModel?.inputPrice
      }),
      uploadRate: 30
    }
    // ,
    // [TrainingModeEnum.chunkUltra]: {
    //   chunkSizeField: 'embeddingChunkSize' as ChunkSizeFieldType,
    //   chunkOverlapRatio: 0.2,
    //   maxChunkSize: vectorModel?.maxToken || 512,
    //   minChunkSize: 100,
    //   autoChunkSize: vectorModel?.defaultToken || 512,
    //   chunkSize: 666,
    //   showChunkInput: true,
    //   showPromptInput: false,
    //   inputPrice: vectorModel.inputPrice,
    //   outputPrice: 0,
    //   priceTip: t('core.dataset.import.Embedding Estimated Price Tips', {
    //     price: vectorModel.inputPrice
    //   }),
    //   uploadRate: 150
    // }
  };
  const selectModelStaticParam = useMemo(() => modeStaticParams[mode], [mode]);

  const wayStaticPrams = {
    [ImportProcessWayEnum.auto]: {
      chunkSize: selectModelStaticParam.autoChunkSize,
      customSplitChar: ''
    },
    [ImportProcessWayEnum.custom]: {
      chunkSize: modeStaticParams[mode].chunkSize,
      customSplitChar
    }
  };

  const chunkSize = wayStaticPrams[way].chunkSize;

  useEffect(() => {
    setShowRePreview(true);
  }, [mode, way, chunkSize, customSplitChar]);

  const totalChunkChars = useMemo(
    () => sources.reduce((sum, file) => sum + file.chunkChars, 0),
    [sources]
  );
  const predictPrice = useMemo(() => {
    if (mode === TrainingModeEnum.qa) {
      const inputTotal = totalChunkChars * selectModelStaticParam.inputPrice;
      const outputTotal = totalChunkChars * 0.5 * selectModelStaticParam.inputPrice;

      return formatModelPrice2Read(inputTotal + outputTotal);
    }
    return formatModelPrice2Read(totalChunkChars * selectModelStaticParam.inputPrice);
  }, [mode, selectModelStaticParam.inputPrice, totalChunkChars]);
  const totalChunks = useMemo(
    () => sources.reduce((sum, file) => sum + file.chunks.length, 0),
    [sources]
  );

  // 前端界面文件上传拆分逻辑

  // wiwewebchange
  const splitSources2Chunks = useCallback(() => {
    setSources((state) =>
      state.map((file) => {
        const { chunks, chars } = splitText2Chunks({
          text: file.rawText,
          chunkLen: chunkSize,
          overlapRatio: selectModelStaticParam.chunkOverlapRatio,
          customReg: customSplitChar ? [customSplitChar] : []
        });

        // 符合拆分后chunks：string[] 和 chunkChars 格式就能大概率引入自定义文本拆分逻辑
        // 使用ts版本llamaindex等框架版本复写逻辑 或 提供接口服务进行数据处理
        /**
                chunks[chunk] 其中chunk结果如下
                 {
                    "q": "你会什么？",
                    "a": "我什么都会",
                    "chunkIndex":"可以不添加",
                    "indexes": [
                        {
                        "defaultIndex": True,
                        "type":"chunk",
                        "text":"自定义索引，不使用默认索引"
                    }]
            }

            原始代码结构物为： 
            chunks: chunks.map((chunk) => ({q:chunk,a:''})
     
         */
        return {
          ...file,
          chunkChars: chars,
          chunks: chunks.map((chunk) => ({
            q: chunk,
            a: ''
          }))
        };
      })
    );
    setShowRePreview(false);
  }, [chunkSize, customSplitChar, selectModelStaticParam.chunkOverlapRatio]);

  // const splitSources2Chunks = useCallback(async () => {
  //   console.log('========splitSources2Chunks============');

  //   const updatedSources = await Promise.all(sources.map(async (file) => {
  //     const response = await fetch('/split-text', { // 注意替换为实际的接口URL
  //       method: 'POST',
  //       headers: {
  //         'Content-Type': 'application/json',
  //       },
  //       body: JSON.stringify({
  //         text: file.rawText,
  //         chunkLen: chunkSize,
  //         overlapRatio: selectModelStaticParam.chunkOverlapRatio,
  //         customReg: customSplitChar ? [customSplitChar] : [],
  //       }),
  //     });

  //     const { chunks, chars } = await response.json();

  //     return {
  //       ...file,
  //       chunkChars: chars,
  //       chunks: chunks.map((chunk: any) => ({
  //         q: chunk,
  //         a: ''
  //       })),
  //     };
  //   }));

  //   setSources(updatedSources);
  //   setShowRePreview(false);
  // }, [sources, chunkSize, customSplitChar, selectModelStaticParam.chunkOverlapRatio]);

  const value = {
    parentId,
    processParamsForm,
    ...selectModelStaticParam,
    sources,
    setSources,
    showRePreview,
    totalChunkChars,
    totalChunks,
    chunkSize,
    predictPrice,
    splitSources2Chunks
  };
  return <StateContext.Provider value={value}>{children}</StateContext.Provider>;
};

export default React.memo(Provider);
