import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { authCert } from '@wiserag/service/support/permission/auth/common';
import { MongoUser } from '@wiserag/service/support/user/schema';
import { connectToDatabase } from '@/service/mongo';
import { hashStr } from '@wiserag/global/common/string/tools';
import { createDefaultTeam } from '@wiserag/service/support/user/team/controller';
import { PRICE_SCALE } from '@wiserag/global/support/wallet/bill/constants';
// 编辑用户接口
async function UserEdit(id: any, name: any, validityPeriod: number) {
  console.log('🚀 ~ UserEdit ~ validityPeriod:', validityPeriod);
  const rootUser = await MongoUser.findOne({ _id: id });

  if (rootUser) {
    // 计算新的过期时间
    const currentExpirationDate = new Date(rootUser.expirationDate);
    const additionalDays = validityPeriod; // 直接使用 validityPeriod 作为天数
    const newExpirationDate = new Date(
      currentExpirationDate.getTime() + additionalDays * 24 * 60 * 60 * 1000
    );

    // 更新用户信息
    const updatedUser = await MongoUser.findOneAndUpdate(
      { _id: id },
      { visualName: name, expirationDate: newExpirationDate }, // 更新过期时间
      { new: true } // 返回更新后的文档
    );

    return updatedUser; // 返回更新后的用户对象
  }
}
export default async function handler(req: NextApiRequest, res: NextApiResponse<any>) {
  try {
    await connectToDatabase();
    const { id, visualName, validityPeriod } = req.body as {
      id: any;
      visualName: string;
      validityPeriod: number;
    };
    const user = await UserEdit(id, visualName, validityPeriod); // 确保 await
    jsonRes(res, {
      data: {
        user
      }
    });
  } catch (err) {
    jsonRes(res, {
      code: 500,
      error: err
    });
  }
}
