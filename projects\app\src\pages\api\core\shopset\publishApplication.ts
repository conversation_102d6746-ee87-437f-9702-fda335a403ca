//订阅接口

import { connectToDatabase } from '@/service/mongo';
import { jsonRes } from '@wiserag/service/common/response';
import { MongoDatasetOutLink } from '@wiserag/service/core/dataset/outlink/schema';
import { NextApiRequest, NextApiResponse } from 'next';

// 判断授权码是否存在
async function checkshareIdIsReal(shareId: string) {
  // 尝试查找匹配的文档
  const document = await MongoDatasetOutLink.findOne({
    shareId: shareId
  }).count();
  const exists = document > 0;
  console.log('========checkshareIdIsReal==============', document, exists);
  return exists;
}

// 判断授权码是否被使用
async function checkUserIdFieldExists(shareId: string) {
  // 尝试查找匹配的文档
  const document = await MongoDatasetOutLink.findOne({
    shareId: shareId,
    status: 1 // 检查是否被绑定
  });
  const exists = document != null;
  console.log('========checkUserIdFieldExists==============', document, exists);
  return exists;
}
async function checkUserIdDataSetExists(userId: string, datasetId: string) {
  // 尝试查找匹配的文档
  const document = await MongoDatasetOutLink.findOne({
    userId: userId,
    datasetId: datasetId,
    status: 1
  });
  // 检查是否找到了文档，并且文档中存在userId字段
  const exists = document != null;
  return exists;
}
// 绑定授权码
async function updateMongoDatasetOutLink(
  shareId: string,
  userId: string,
  datasetId: string,
  usedTime: any
) {
  // 尝试查找匹配的文档
  const dataSetShareBasicInfo = await MongoDatasetOutLink.findOne({ shareId });
  const _id = dataSetShareBasicInfo?._id;
  const document = await MongoDatasetOutLink.updateOne(
    { _id: _id },
    { $set: { userId: userId, datasetId: datasetId, usedTime: usedTime, status: 1 } }
  );
  console.log('===============updateMongoDatasetOutLink==============');
  console.log(document.acknowledged);
  return document.acknowledged;
}
export default async function handler(req: NextApiRequest, res: NextApiResponse<any>) {
  try {
    await connectToDatabase();
    const { shareId, userId, datasetId, usedTime } = req.body as {
      shareId: string;
      userId: string;
      datasetId: string;
      usedTime: any;
    };
    const shareIdisUsed = await checkUserIdFieldExists(shareId);
    const shareIdisReal = await checkshareIdIsReal(shareId);
    const datasetIdisUsed = await checkUserIdDataSetExists(userId, datasetId);

    if (!shareIdisReal) {
      console.log('该授权码无效', shareId);
      return jsonRes(res, {
        code: 200,
        data: '无效授权码',
        message: '无效授权码'
      });
    } else if (shareIdisUsed || datasetIdisUsed) {
      console.log('该授权码已被绑定使用', shareId, shareIdisUsed, datasetIdisUsed);
      return jsonRes(res, {
        code: 200,
        data: '该授权码已被绑定使用',
        message: '该授权码已被绑定使用'
      });
    } else {
      console.log('开始绑定授权码');
      const result = await updateMongoDatasetOutLink(shareId, userId, datasetId, usedTime);
      return jsonRes(res, { code: 200, data: '绑定成功', message: '绑定成功' });
    }
  } catch (err) {
    jsonRes(res, {
      code: 500,
      error: err
    });
  }
}
