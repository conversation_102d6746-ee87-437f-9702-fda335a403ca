import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { connectToDatabase } from '@/service/mongo';
import { MongoDatasetOutLink } from '@wiserag/service/core/dataset/outlink/schema';
import { ShareDatasetWithDataSetSchema } from '@wiserag/global/core/dataset/type';

//查询授权码列表
async function getDataSetOutLinkShareDataCodeStatus(pageNum: number, pageSize: number) {
  const skipCount = (pageNum - 1) * pageSize;
  const datas = (await MongoDatasetOutLink.find()
    .populate('datasetId')
    .skip(skipCount)
    .limit(pageSize)) as unknown as ShareDatasetWithDataSetSchema[];
  return datas.map((item) => ({
    _id: item._id, // 唯一标识不展示
    userId: item.userId ? item.userId : '', // 已绑定的用户id-展示
    shareId: item.shareId, // 授权码展示
    type: item.type, // 数据库类型 如果为sharedDataset显示为订阅数据库
    datasetId: item.datasetId ? item.datasetId._id : '', // 不展示
    datasetName: item.datasetId ? item.datasetId.name : '', // 绑定的知识库名称
    expiredTime: item.limit?.expiredTime, // 订阅过期时间
    createTime: item.createTime, // 授权码创建时间
    usedTime: item.usedTime, //  授权码绑定时间
    status: item.status // 授权码状态 0-未使用，1-使用，-1-过期
  }));
}

export default async function handler(req: NextApiRequest, res: NextApiResponse<any>) {
  try {
    await connectToDatabase();
    const { pageNum, pageSize } = req.body as {
      pageNum: number;
      pageSize: number;
    };
    const shareDataCodeStatus = await getDataSetOutLinkShareDataCodeStatus(pageNum, pageSize);
    const totalshareDataCodeStatus = await MongoDatasetOutLink.countDocuments({}); // 获取用户总数

    jsonRes(res, {
      // pageNum,
      // pageSize,
      // data: shareDataCodeStatus,
      // total: shareDataCodeStatus.length
      data: {
        data: shareDataCodeStatus,
        pageNum,
        pageSize,
        total: totalshareDataCodeStatus
      }
    });
  } catch (err) {
    jsonRes(res, {
      code: 500,
      error: err
    });
  }
}
