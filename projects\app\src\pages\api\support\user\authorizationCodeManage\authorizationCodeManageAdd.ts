import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { connectToDatabase } from '@/service/mongo';
import { customAlphabet } from 'nanoid';
import { MongoDatasetOutLink } from '@wiserag/service/core/dataset/outlink/schema';
/**
 * 授权码生成逻辑
 */
async function createDataSetOutLinkShareDataCode(expiredMonts: number, expiredCount: number) {
  const nanoid = customAlphabet('abcdefghijklmnopqrstuvwxyz1234567890', 24);
  const dataSetOutLinkShareId = nanoid();
  let expiredTimeResult = new Date();
  let expiredTimeResultFinal = expiredTimeResult.setMonth(
    expiredTimeResult.getMonth() + expiredMonts
  );
  const { _id, shareId } = await MongoDatasetOutLink.create({
    shareId: dataSetOutLinkShareId,
    limit: {
      expiredTime: expiredTimeResultFinal, // 过期时间，已创建时间为开始基准，有效时间在前端进行枚举选择，这里只记录结果
      expiredCount: expiredCount // -1 标识无限制，暂时为占位字段
    },
    status: 0
  });
  return shareId;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse<any>) {
  try {
    await connectToDatabase();
    // const { expiredMonts, expiredCount } = req.body as { expiredMonts: string; expiredCount: string };
    const expiredMonts: number = 3; // 以月份为基准，默认值自己决定
    const expiredCount: number = -1; // 标识无限制，暂时为占位字段
    const shareId = await createDataSetOutLinkShareDataCode(expiredMonts, expiredCount);
    jsonRes(res, {
      data: {
        shareId
      }
    });
  } catch (err) {
    jsonRes(res, {
      code: 500,
      error: err
    });
  }
}
