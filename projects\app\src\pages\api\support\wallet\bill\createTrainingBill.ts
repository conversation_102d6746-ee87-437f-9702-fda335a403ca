import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { connectToDatabase } from '@/service/mongo';
import { BillSourceEnum } from '@wiserag/global/support/wallet/bill/constants';
import { CreateTrainingBillProps } from '@wiserag/global/support/wallet/bill/api.d';
import { getLLMModel, getVectorModel } from '@/service/core/ai/model';
import { createTrainingBill } from '@wiserag/service/support/wallet/bill/controller';
import { authDataset, authDatasetNew } from '@wiserag/service/support/permission/auth/dataset';
import { MongoDataset } from '@wiserag/service/core/dataset/schema';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    await connectToDatabase();
    const { name, datasetId } = req.body as CreateTrainingBillProps;

    // const { teamId, tmbId, dataset } = await authDataset({
    //   req,
    //   authToken: true,
    //   authApiKey: true,
    //   datasetId,
    //   per: 'w'
    // });

    // change
    const baseInfo = await MongoDataset.findOne({ _id: datasetId });
    const teamId = String(baseInfo?.teamId);
    const { dataset, tmbId } = await authDatasetNew({
      req,
      authToken: true,
      authApiKey: true,
      datasetId,
      teamId,
      per: 'w'
    });

    const { billId } = await createTrainingBill({
      teamId,
      tmbId,
      appName: name,
      billSource: BillSourceEnum.training,
      vectorModel: getVectorModel(dataset.vectorModel).name,
      agentModel: getLLMModel(dataset.agentModel).name
    });

    jsonRes<string>(res, {
      data: billId
    });
  } catch (err) {
    jsonRes(res, {
      code: 500,
      error: err
    });
  }
}
