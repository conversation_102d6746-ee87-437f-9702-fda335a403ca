import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { authCert } from '@wiserag/service/support/permission/auth/common';
import { MongoUser } from '@wiserag/service/support/user/schema';
import { connectToDatabase } from '@/service/mongo';
import { ERROR_ENUM } from '@wiserag/global/common/error/errorCode';
import { TeamMemberSchema, TeamMemberWithUserSchema } from '@wiserag/global/support/user/team/type';
import { TeamMemberRoleEnum, notLeaveStatus } from '@wiserag/global/support/user/team/constant';
import { MongoTeamMember } from '@wiserag/service/support/user/team/teamMemberSchema';
import { Types } from '@wiserag/service/common/mongo';
import { AppLogsListItemType } from '@/types/app';
import { PagingData } from '@/types';
// import { usePagination } from '@/web/common/hooks/usePagination';

export async function getAllUsersDetails(pageNum: number, pageSize: number): Promise<any[]> {
  const users = await MongoUser.find({})
    .skip((pageNum - 1) * pageSize)
    .limit(pageSize);

  // if (!users || users.length === 0) {
  //   return Promise.reject(ERROR_ENUM.unAuthorization);
  // }

  return users.map((user) => ({
    _id: String(user._id),
    username: user.username,
    avatar: user.avatar,
    balance: user.balance,
    timezone: user.timezone,
    visualName: user.visualName,
    expirationDate: user.expirationDate,
    createTime: user.createTime
  }));
}

export async function getTeamMemberWithUserInfos(
  match: Record<string, any>,
  pageNum: number,
  pageSize: number
): Promise<TeamMemberSchema[]> {
  const skipCount = (pageNum - 1) * pageSize;
  const tmbs = (await MongoTeamMember.find(match)
    .populate('userId')
    .skip(skipCount)
    .limit(pageSize)) as unknown as TeamMemberWithUserSchema[];

  if (!tmbs || tmbs.length === 0) {
    return Promise.reject('No members found');
  }

  return tmbs.map((tmb) => ({
    username: tmb.userId.username,
    visualName: tmb.userId.visualName,
    userId: String(tmb.userId._id),
    name: tmb.name,
    teamId: String(tmb.teamId),
    tmbId: String(tmb._id),
    role: tmb.role,
    status: tmb.status,
    defaultTeam: tmb.defaultTeam,
    canWrite: tmb.role !== TeamMemberRoleEnum.visitor,
    _id: String(tmb._id),
    balance: tmb.userId.balance,
    createTime: tmb.createTime
  }));
}

export async function getTmbWithUserInfoByTeamId(
  { teamId }: { teamId: string },
  pageNum: number,
  pageSize: number
) {
  if (!teamId) {
    return Promise.reject('teamId is required');
  }

  return getTeamMemberWithUserInfos(
    {
      teamId: new Types.ObjectId(teamId),
      status: notLeaveStatus
    },
    pageNum,
    pageSize
  );
}

export async function getTmbWithUserInfoByuserName(
  { userName }: { userName: any },
  pageNum: number,
  pageSize: number
) {
  const data = await MongoUser.find({
    $or: [
      { username: { $regex: new RegExp(userName, 'i') } },
      { visualName: { $regex: new RegExp(userName, 'i') } }
    ]
  })
    .skip((pageNum - 1) * pageSize)
    .limit(pageSize);

  return data.map((user) => ({
    _id: String(user._id),
    username: user.username,
    avatar: user.avatar,
    balance: user.balance,
    timezone: user.timezone,
    visualName: user.visualName
  }));
}

export default async function handler(req: NextApiRequest, res: NextApiResponse<any>) {
  try {
    await connectToDatabase();
    const { teamId, userName, pageNum, pageSize } = req.body as {
      teamId: string;
      userName: string;
      pageNum: number;
      pageSize: number;
    };
    // const { userId } = await authCert({ req, authToken: true });

    let user: any[] = [];

    if (userName) {
      user = await getTmbWithUserInfoByuserName({ userName }, pageNum, pageSize);
    } else if (teamId) {
      user = await getTmbWithUserInfoByTeamId({ teamId }, pageNum, pageSize);
    } else {
      user = await getAllUsersDetails(pageNum, pageSize);
    }
    const totalUsers = await MongoUser.countDocuments({}); // 获取用户总数

    if (!user) {
      throw new Error('users is error');
    }

    jsonRes(res, {
      // pageNum,
      // pageSize,
      // data: user,
      // total: totalUsers
      data: {
        data: user,
        pageNum,
        pageSize,
        total: totalUsers
      }
    });
  } catch (err) {
    jsonRes(res, {
      code: 500,
      error: err
    });
  }
}
