import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { authCert } from '@wiserag/service/support/permission/auth/common';
import { MongoUser } from '@wiserag/service/support/user/schema';
import { connectToDatabase } from '@/service/mongo';

import { ERROR_ENUM } from '@wiserag/global/common/error/errorCode';
import { TeamMemberSchema, TeamMemberWithUserSchema } from '@wiserag/global/support/user/team/type';
import {
  TeamMemberRoleEnum,
  TeamMemberStatusEnum,
  notLeaveStatus
} from '@wiserag/global/support/user/team/constant';
import { MongoTeamMember } from '@wiserag/service/support/user/team/teamMemberSchema';
import { Types } from '@wiserag/service/common/mongo';

//查询团队列表
export async function deleteTeamMember(id: string): Promise<boolean> {
  try {
    if (!id) {
      return false; // id为空，返回false
    }
    // 根据id查找团队成员
    const tmb = await MongoTeamMember.findById(id);
    if (tmb) {
      // 成员存在，执行删除操作
      await MongoTeamMember.deleteOne({ _id: id });
      return true; // 操作成功，返回true
    } else {
      return false; // 成员不存在，返回false
    }
  } catch (err) {
    throw new Error('Failed to delete team member: ' + err);
  }
}
export default async function handler(req: NextApiRequest, res: NextApiResponse<any>) {
  try {
    await connectToDatabase();
    // const { userId } = await authCert({ req, authToken: true });
    const { deleteId } = req.query as { deleteId: any };
    const data = await deleteTeamMember(deleteId);

    jsonRes(res, {
      data: data
    });
  } catch (err) {
    jsonRes(res, {
      code: 500,
      error: err
    });
  }
}
