import { FlowNodeTypeEnum } from '@wiserag/global/core/module/node/constant';

/* 此 TypeScript
代码片段定义了一个常量对象“initRunningModuleType”，其键为“string”类型，值为“boolean”类型。该对象使用三个键值对进行初始化，其中键是“FlowNodeTypeEnum”中的枚举值，值全部设置为“true”。该对象用于指定流程中不同节点类型的初始运行模块。 */
export const initRunningModuleType: Record<string, boolean> = {
  [FlowNodeTypeEnum.historyNode]: true,
  [FlowNodeTypeEnum.questionInput]: true,
  [FlowNodeTypeEnum.pluginInput]: true
};
