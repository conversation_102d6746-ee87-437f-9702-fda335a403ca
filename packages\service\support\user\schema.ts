import { connectionMongo, type Model } from '../../common/mongo';
const { Schema, model, models } = connectionMongo;
import { hashStr } from '@wiserag/global/common/string/tools';
import { PRICE_SCALE } from '@wiserag/global/support/wallet/bill/constants';
import type { UserModelSchema } from '@wiserag/global/support/user/type';
import { UserStatusEnum, userStatusMap } from '@wiserag/global/support/user/constant';

export const userCollectionName = 'users';

const UserSchema = new Schema({
  status: {
    type: String,
    enum: Object.keys(userStatusMap),
    default: UserStatusEnum.active
  },
  username: {
    // 可以是手机/邮箱，新的验证都只用手机
    type: String,
    required: true
  },
  token: {
    type: String,
    // unique: true // 唯一
  },
  visualName: {
    type: String
  },
  password: {
    type: String,
    required: true,
    set: (val: string) => hashStr(val),
    get: (val: string) => hashStr(val),
    select: false
  },
  createTime: {
    type: Date,
    default: () => new Date()
  },
  avatar: {
    type: String,
    default: '/icon/human.svg'
  },
  balance: {
    type: Number,
    default: 2 * PRICE_SCALE
  },
  inviterId: {
    // 谁邀请注册的
    type: Schema.Types.ObjectId,
    ref: 'user'
  },
  promotionRate: {
    type: Number,
    default: 15
  },
  openaiAccount: {
    type: {
      key: String,
      baseUrl: String
    }
  },
  timezone: {
    type: String,
    default: 'Asia/Shanghai'
  },
  expirationDate: {
    // 用户使用到期时间
    type: Date,
    default: () => new Date(new Date().getTime() + 180 * 24 * 60 * 60 * 1000)
  }
});

try {
  UserSchema.index({ createTime: -1 });
} catch (error) {
  console.log(error);
}

export const MongoUser: Model<UserModelSchema> =
  models[userCollectionName] || model(userCollectionName, UserSchema);
MongoUser.syncIndexes();
