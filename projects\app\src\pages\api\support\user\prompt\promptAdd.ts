import { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { authCert } from '@wiserag/service/support/permission/auth/common';
import { MongoPrompt } from '@wiserag/service/support/user/prompt';
import { connectToDatabase } from '@/service/mongo';

// 增加prompt字段接口
export default async function handler(req: NextApiRequest, res: NextApiResponse<any>) {
  try {
    await connectToDatabase();
    const { title, desc, aiChatQuoteTemplate, aiChatQuotePrompt } = req.body as {
      title: string;
      desc: string;
      aiChatQuoteTemplate: string;
      aiChatQuotePrompt: string;
    };

    const newPrompt = new MongoPrompt({
      title: title,
      desc: desc,
      aiChatQuoteTemplate: aiChatQuoteTemplate,
      aiChatQuotePrompt: aiChatQuotePrompt
    });

    const result = await newPrompt.save();
    jsonRes(res, {
      data: result
    });
  } catch (err) {
    jsonRes(res, {
      code: 500,
      error: err
    });
  }
}
