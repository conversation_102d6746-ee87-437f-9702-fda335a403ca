import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { authCert } from '@wiserag/service/support/permission/auth/common';
import { MongoUser } from '@wiserag/service/support/user/schema';
import { connectToDatabase } from '@/service/mongo';

import { ERROR_ENUM } from '@wiserag/global/common/error/errorCode';
import { TeamMemberSchema, TeamMemberWithUserSchema } from '@wiserag/global/support/user/team/type';
import { TeamMemberRoleEnum, notLeaveStatus } from '@wiserag/global/support/user/team/constant';
import { MongoTeamMember } from '@wiserag/service/support/user/team/teamMemberSchema';
import { Types } from '@wiserag/service/common/mongo';

//查询团队列表
async function getTeamMemberWithUserInfos(
  match: Record<string, any>,
  pageNum: number,
  pageSize: number
): Promise<{ data: TeamMemberSchema[]; pageNum: number; pageSize: number; total: number }> {
  const skip = (pageNum - 1) * pageSize;
  const limit = pageSize;
  const tmbs = (await MongoTeamMember.find(match)
    .skip(skip)
    .limit(limit)
    .populate('userId')) as TeamMemberWithUserSchema[];
  if (!tmbs || tmbs.length === 0) {
    return Promise.reject('No members found');
  }
  const total = await MongoTeamMember.countDocuments(match);
  return {
    data: tmbs.map((tmb) => ({
      username: tmb.userId.username,
      visualName: tmb.userId.visualName,
      userId: String(tmb.userId._id),
      name: tmb.name,
      teamId: String(tmb.teamId),
      tmbId: String(tmb._id),
      role: tmb.role,
      status: tmb.status,
      defaultTeam: tmb.defaultTeam,
      canWrite: tmb.role !== TeamMemberRoleEnum.visitor,
      _id: String(tmb._id),
      createTime: tmb.createTime
    })),
    pageNum,
    pageSize,
    total
  };
}
export async function getTmbWithUserInfoByTeamId({
  teamId,
  pageNum,
  pageSize
}: {
  teamId: string;
  pageNum: number;
  pageSize: number;
}) {
  if (!teamId) {
    return Promise.reject('teamId is required');
  }
  return getTeamMemberWithUserInfos(
    {
      teamId: new Types.ObjectId(teamId),
      status: notLeaveStatus
    },
    pageNum,
    pageSize
  );
}

export default async function handler(req: NextApiRequest, res: NextApiResponse<any>) {
  try {
    await connectToDatabase();
    // const { teamId } = await authCert({ req, authToken: true }); //用户teamId
    const { teamIdparams, pageNum, pageSize } = req.body as {
      teamIdparams: string;
      pageNum: number;
      pageSize: number;
    }; //传入teamId
    let teamMemberWithUserInfos: {
      data: TeamMemberSchema[];
      pageNum: number;
      pageSize: number;
      total: number;
    } = { data: [], pageNum, pageSize, total: 0 };
    if (teamIdparams) {
      teamMemberWithUserInfos = await getTmbWithUserInfoByTeamId({
        teamId: String(teamIdparams),
        pageNum,
        pageSize
      });
    }
    jsonRes(res, {
      // pageNum,
      // pageSize,
      // data: teamMemberWithUserInfos,
      // total: teamMemberWithUserInfos.length
      data: {
        data: teamMemberWithUserInfos.data,
        pageNum: teamMemberWithUserInfos.pageNum,
        pageSize: teamMemberWithUserInfos.pageSize,
        total: teamMemberWithUserInfos.total
      }
    });
  } catch (err) {
    jsonRes(res, {
      code: 500,
      error: err
    });
  }
}
