import type { NextApiResponse } from 'next';
import { ChatContextFilter } from '@wiserag/service/core/chat/utils';
import type { moduleDispatchResType, ChatItemType } from '@wiserag/global/core/chat/type.d';
import { ChatRoleEnum } from '@wiserag/global/core/chat/constants';
import { sseResponseEventEnum } from '@wiserag/service/common/response/constant';
import { textAdaptGptResponse } from '@/utils/adapt';
import { getAIApi } from '@wiserag/service/core/ai/config';
import type { ChatCompletion, StreamChatType } from '@wiserag/global/core/ai/type.d';
import { formatModelPrice2Store } from '@/service/support/wallet/bill/utils';
import type { LLMModelItemType } from '@wiserag/global/core/ai/model.d';
import { postTextCensor } from '@/service/common/censor';
import { ChatCompletionRequestMessageRoleEnum } from '@wiserag/global/core/ai/constant';
import type { ModuleItemType } from '@wiserag/global/core/module/type.d';
import { countMessagesTokens, sliceMessagesTB } from '@wiserag/global/common/string/tiktoken';
import { adaptChat2GptMessages } from '@wiserag/global/core/chat/adapt';
import { Prompt_QuotePromptList, Prompt_QuoteTemplateList } from '@/global/core/prompt/AIChat';
import type { AIChatModuleProps } from '@wiserag/global/core/module/node/type.d';
import { replaceVariable } from '@wiserag/global/common/string/tools';
import type { ModuleDispatchProps } from '@wiserag/global/core/module/type.d';
import { responseWrite, responseWriteController } from '@wiserag/service/common/response';
import { getLLMModel, ModelTypeEnum } from '@/service/core/ai/model';
import type { SearchDataResponseItemType } from '@wiserag/global/core/dataset/type';
import { formatStr2ChatContent } from '@wiserag/service/core/chat/utils';
import { ModuleInputKeyEnum, ModuleOutputKeyEnum } from '@wiserag/global/core/module/constants';
import { getHistories } from '../utils';
import { filterSearchResultsByMaxChars } from '@wiserag/global/core/dataset/search/utils';

export type ChatProps = ModuleDispatchProps<
  AIChatModuleProps & {
    [ModuleInputKeyEnum.userChatInput]: string;
    [ModuleInputKeyEnum.history]?: ChatItemType[] | number;
    [ModuleInputKeyEnum.aiChatDatasetQuote]?: SearchDataResponseItemType[];
  }
>;
export type ChatResponse = {
  [ModuleOutputKeyEnum.answerText]: string;
  [ModuleOutputKeyEnum.responseData]: moduleDispatchResType;
  [ModuleOutputKeyEnum.history]: ChatItemType[];
};

/* request openai chat */
export const dispatchChatCompletion = async (props: ChatProps): Promise<ChatResponse> => {
  let {
    res,
    stream = false,
    detail = false,
    user,
    histories,
    outputs,
    params: {
      model,
      temperature = 0,
      maxToken = 4000,
      history = 6,
      quoteQA = [],
      userChatInput,
      isResponseAnswerText = true,
      systemPrompt = '',
      quoteTemplate,
      quotePrompt
    }
  } = props;
  if (!userChatInput) {
    return Promise.reject('Question is empty');
  }

  stream = stream && isResponseAnswerText;
  console.log('stream', stream, 'isResponseAnswerText', isResponseAnswerText, 'maxToken', maxToken);

  const chatHistories = getHistories(history, histories);

  // temperature adapt
  const modelConstantsData = getLLMModel(model);

  if (!modelConstantsData) {
    return Promise.reject('The chat model is undefined, you need to select a chat model.');
  }

  const { filterQuoteQA, quoteText } = filterQuote({
    quoteQA,
    model: modelConstantsData,
    quoteTemplate
  });

  // censor model and system key
  if (modelConstantsData.censor && !user.openaiAccount?.key) {
    await postTextCensor({
      text: `${systemPrompt}
      ${quoteText}
      ${userChatInput}
      `
    });
  }

  const { messages, filterMessages } = getChatMessages({
    model: modelConstantsData,
    histories: chatHistories,
    quoteText,
    quotePrompt,
    userChatInput,
    systemPrompt
  });
  console.log('max_tokens', maxToken);
  const { max_tokens } = getMaxTokens({
    model: modelConstantsData,
    maxToken,
    filterMessages
  });
  console.log('max_tokens1', max_tokens);

  //  temperature range: 1~10
  temperature = +(modelConstantsData.maxTemperature * (temperature / 10)).toFixed(2);
  temperature = Math.max(temperature, 0.01);
  const ai = getAIApi({
    userKey: user.openaiAccount,
    timeout: 480000
  });

  const concatMessages = [
    ...(modelConstantsData.defaultSystemChatPrompt
      ? [
          {
            role: ChatCompletionRequestMessageRoleEnum.System,
            content: modelConstantsData.defaultSystemChatPrompt
          }
        ]
      : []),
    ...(await Promise.all(
      messages.map(async (item) => ({
        ...item,
        content: modelConstantsData.vision
          ? await formatStr2ChatContent(item.content)
          : item.content
      }))
    ))
  ];

  if (concatMessages.length === 0) {
    return Promise.reject('core.chat.error.Messages empty');
  }
  console.log('max_tokens2', max_tokens);
  console.log('ai.chat.completions.create 请求参数', {
    presence_penalty: 0,
    frequency_penalty: 0,
    ...modelConstantsData?.defaultConfig,
    model: modelConstantsData.model,
    temperature,
    max_tokens,
    stream,
    messages: concatMessages
  });

  const response = await ai.chat.completions.create(
    {
      presence_penalty: 0,
      frequency_penalty: 0, // glm3 惩罚不能为非0数字，可在config.json llm设置中设置defaultConfig中添加参数
      ...modelConstantsData?.defaultConfig,
      model: modelConstantsData.model,
      temperature,
      max_tokens, // 新版本接入oneapi的千问官方需要设置max_tokens为[1,2000]，通过前端界面设置回复上限
      stream,
      messages: concatMessages
    },
    {
      headers: {
        Accept: 'application/json, text/plain, */*'
      }
    }
  );
  /**
   * presence_penalty：数字，选填，默认为0。
    存在惩罚，-2.0 和 2.0 之间的数字。正值会根据到目前为止是否出现在文本中来惩罚新标记，从而增加模型谈论新主题的可能性。

    frequency_penalty：数字，选填，默认为0。
    频率惩罚，-2.0 和 2.0 之间的数字。正值会根据新标记在文本中的现有频率对其进行惩罚，从而降低模型逐字重复同一行的可能性

   */

  console.log('max_tokens3', max_tokens);

  const { answerText, inputTokens, outputTokens, completeMessages } = await (async () => {
    if (stream) {
      // sse response
      const { answer } = await streamResponse({
        res,
        detail,
        stream: response
      });
      // count tokens
      const completeMessages = filterMessages.concat({
        obj: ChatRoleEnum.AI,
        value: answer
      });

      targetResponse({ res, detail, outputs });

      return {
        answerText: answer,
        inputTokens: countMessagesTokens({
          messages: filterMessages
        }),
        outputTokens: countMessagesTokens({
          messages: [
            {
              obj: ChatRoleEnum.AI,
              value: answer
            }
          ]
        }),
        completeMessages
      };
    } else {
      const unStreamResponse = response as ChatCompletion;
      const answer = unStreamResponse.choices?.[0]?.message?.content || '';

      const completeMessages = filterMessages.concat({
        obj: ChatRoleEnum.AI,
        value: answer
      });

      return {
        answerText: answer,
        inputTokens: unStreamResponse.usage?.prompt_tokens || 0,
        outputTokens: unStreamResponse.usage?.completion_tokens || 0,
        completeMessages
      };
    }
  })();

  const { total, modelName } = formatModelPrice2Store({
    model,
    inputLen: inputTokens,
    outputLen: outputTokens,
    type: ModelTypeEnum.llm
  });

  return {
    answerText,
    responseData: {
      price: user.openaiAccount?.key ? 0 : total,
      model: modelName,
      inputTokens,
      outputTokens,
      query: `${userChatInput}`,
      maxToken: max_tokens,
      quoteList: filterQuoteQA,
      historyPreview: getHistoryPreview(completeMessages),
      contextTotalLen: completeMessages.length
    },
    history: completeMessages
  };
};

/**
 * 函数“filterQuote”处理报价问答对列表，根据特定条件对它们进行过滤和排序，并生成格式化的报价文本。
 *
 * @param  `filterQuote` 函数接受以下参数：
 * @return `filterQuote` 函数返回一个具有两个属性的对象：
 * 1. `filterQuoteQA`：根据特定条件过滤和排序的 `quoteQA` 项目的数组。
 * 2. `quoteText`：一个字符串，用于连接已过滤和排序的 `quoteQA` 项的值，并以换行符分隔。
 */
function filterQuote({
  quoteQA = [],
  model,
  quoteTemplate
}: {
  quoteQA: ChatProps['params']['quoteQA'];
  model: LLMModelItemType;
  quoteTemplate?: string;
}) {
  function getValue(item: SearchDataResponseItemType, index: number) {
    return replaceVariable(quoteTemplate || Prompt_QuoteTemplateList[0].value, {
      q: item.q,
      a: item.a,
      source: item.sourceName,
      sourceId: String(item.sourceId || 'UnKnow'),
      index: index + 1
    });
  }
  // 切片 根据token计算返回引用

  // slice filterSearch
  const filterQuoteQA = filterSearchResultsByMaxChars(quoteQA, model.quoteMaxToken);

  // filterQuoteQA按collectionId聚合在一起后，再按chunkIndex从小到大排序
  const sortQuoteQAMap: Record<string, SearchDataResponseItemType[]> = {};
  filterQuoteQA.forEach((item) => {
    if (sortQuoteQAMap[item.collectionId]) {
      sortQuoteQAMap[item.collectionId].push(item);
    } else {
      sortQuoteQAMap[item.collectionId] = [item];
    }
  });
  const sortQuoteQAList = Object.values(sortQuoteQAMap);

  sortQuoteQAList.forEach((qaList) => {
    qaList.sort((a, b) => a.chunkIndex - b.chunkIndex);
  });

  const flatQuoteList = sortQuoteQAList.flat();

  const quoteText =
    flatQuoteList.length > 0
      ? `${flatQuoteList.map((item, index) => getValue(item, index)).join('\n')}`
      : '';

  return {
    filterQuoteQA: flatQuoteList,
    quoteText
  };
}
function getChatMessages({
  quotePrompt,
  quoteText,
  histories = [],
  systemPrompt,
  userChatInput,
  model
}: {
  quotePrompt?: string;
  quoteText: string;
  histories: ChatItemType[];
  systemPrompt: string;
  userChatInput: string;
  model: LLMModelItemType;
}) {
  const question = quoteText
    ? replaceVariable(quotePrompt || Prompt_QuotePromptList[0].value, {
        quote: quoteText,
        question: userChatInput
      })
    : userChatInput;

  const messages: ChatItemType[] = [
    ...(systemPrompt
      ? [
          {
            obj: ChatRoleEnum.System,
            value: systemPrompt
          }
        ]
      : []),
    ...histories,
    {
      obj: ChatRoleEnum.Human,
      value: question
    }
  ];

  const filterMessages = ChatContextFilter({
    messages,
    maxTokens: Math.ceil(model.maxContext - 300) // filter token. not response maxToken
  });

  const adaptMessages = adaptChat2GptMessages({ messages: filterMessages, reserveId: false });

  return {
    messages: adaptMessages,
    filterMessages
  };
}
function getMaxTokens({
  maxToken,
  model,
  filterMessages = []
}: {
  maxToken: number;
  model: LLMModelItemType;
  filterMessages: ChatItemType[];
}) {
  maxToken = Math.min(maxToken, model.maxResponse);
  const tokensLimit = model.maxContext;

  /* count response max token */
  const promptsToken = countMessagesTokens({
    messages: filterMessages
  });
  maxToken = promptsToken + maxToken > tokensLimit ? tokensLimit - promptsToken : maxToken;

  return {
    max_tokens: maxToken
  };
}

function targetResponse({
  res,
  outputs,
  detail
}: {
  res: NextApiResponse;
  outputs: ModuleItemType['outputs'];
  detail: boolean;
}) {
  const targets =
    outputs.find((output) => output.key === ModuleOutputKeyEnum.answerText)?.targets || [];

  if (targets.length === 0) return;
  responseWrite({
    res,
    event: detail ? sseResponseEventEnum.answer : undefined,
    data: textAdaptGptResponse({
      text: '\n'
    })
  });
}

async function streamResponse({
  res,
  detail,
  stream
}: {
  res: NextApiResponse;
  detail: boolean;
  stream: StreamChatType;
}) {
  const write = responseWriteController({
    res,
    readStream: stream
  });

  let answer = '';
  for await (const part of stream) {
    if (res.closed) {
      console.log('res.closed');
      stream.controller?.abort();
      break;
    }
    const content = part.choices?.[0]?.delta?.content || '';
    answer += content;

    responseWrite({
      write,
      event: detail ? sseResponseEventEnum.answer : undefined,
      data: textAdaptGptResponse({
        text: content
      })
    });
  }

  if (!answer) {
    return Promise.reject('core.chat.Chat API is error or undefined');
  }

  return { answer };
}

function getHistoryPreview(completeMessages: ChatItemType[]) {
  return completeMessages.map((item, i) => {
    if (item.obj === ChatRoleEnum.System) return item;
    if (i >= completeMessages.length - 2) return item;
    return {
      ...item,
      value: item.value.length > 15 ? `${item.value.slice(0, 15)}...` : item.value
    };
  });
}
