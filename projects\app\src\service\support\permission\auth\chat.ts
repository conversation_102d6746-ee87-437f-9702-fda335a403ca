import { ChatSchema } from '@wiserag/global/core/chat/type';
import { MongoChat } from '@wiserag/service/core/chat/chatSchema';
import { AuthModeType } from '@wiserag/service/support/permission/type';
import { authOutLink } from './outLink';
import { ChatErrEnum } from '@wiserag/global/common/error/code/chat';
import { authUserRole } from '@wiserag/service/support/permission/auth/user';
import { TeamMemberRoleEnum } from '@wiserag/global/support/user/team/constant';
import { AuthResponseType } from '@wiserag/global/support/permission/type';

/* 
  outLink: Must be the owner
  token: team owner and chat owner have all permissions
*/
/**
 * TypeScript 中的函数“autChatCrud”根据各种参数处理聊天的身份验证和 CRUD 操作。
 *
 * @param  `autChatCrud` 函数是一个异步函数，它接受一个对象作为其参数。该对象应具有以下属性：
 * @return `autChatCrud` 函数返回一个 Promise，该 Promise 解析为具有以下属性的对象：
 * - `chat`：使用提供的 `appId` 和 `chatId` 从数据库检索的 ChatSchema 对象。
 * - `isOutLink`：一个布尔值，指示聊天是否是基于 `shareId` 和 `outLinkUid` 的存在的外链聊天。
 * - `uid`:
 */

export async function autChatCrud({
  appId,
  chatId,
  shareId,
  outLinkUid,
  per = 'owner',
  ...props
}: AuthModeType & {
  appId: string;
  chatId?: string;
  shareId?: string;
  outLinkUid?: string;
}): Promise<{
  chat?: ChatSchema;
  isOutLink: boolean;
  uid?: string;
}> {
  const isOutLink = Boolean(shareId && outLinkUid);
  if (!chatId) return { isOutLink, uid: outLinkUid };

  const chat = await MongoChat.findOne({ appId, chatId }).lean();

  if (!chat) return { isOutLink, uid: outLinkUid };

  const { uid } = await (async () => {
    // outLink Auth
    if (shareId && outLinkUid) {
      const { uid } = await authOutLink({ shareId, outLinkUid });

      // auth outLinkUid
      if (chat.shareId === shareId && chat.outLinkUid === uid) {
        return { uid };
      }
      return Promise.reject(ChatErrEnum.unAuthChat);
    }

    // req auth
    const { teamId, tmbId, role } = await authUserRole(props);

    if (String(teamId) !== String(chat.teamId)) return Promise.reject(ChatErrEnum.unAuthChat);

    if (role === TeamMemberRoleEnum.owner) return { uid: outLinkUid };
    if (String(tmbId) === String(chat.tmbId)) return { uid: outLinkUid };

    // admin
    if (per === 'r' && role === TeamMemberRoleEnum.admin) return { uid: outLinkUid };

    return Promise.reject(ChatErrEnum.unAuthChat);
  })();

  return {
    chat,
    isOutLink,
    uid
  };
}
