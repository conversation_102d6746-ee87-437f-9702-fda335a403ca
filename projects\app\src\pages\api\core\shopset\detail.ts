import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { connectToDatabase } from '@/service/mongo';
import { getLLMModel, getVectorModel } from '@/service/core/ai/model';
import type { DatasetItemType } from '@wiserag/global/core/dataset/type.d';
import { authDataset, authDatasetFDetail } from '@wiserag/service/support/permission/auth/dataset';
import { MongoDataset } from '@wiserag/service/core/dataset/schema';

export default async function handler(req: NextApiRequest, res: NextApiResponse<any>) {
  try {
    await connectToDatabase();
    const { id: datasetId } = req.query as {
      id: string;
    };

    if (!datasetId) {
      throw new Error('缺少参数');
    }

    // 凭证校验
    // const { dataset, canWrite, isOwner } = await authDataset({
    // Change
    // '=============shopset知识库详情查看-start============'
    const teamId = await MongoDataset.findOne({ _id: datasetId });
    const { dataset, canWrite, isOwner } = await authDatasetFDetail({
      req,
      authToken: true,
      authApiKey: true,
      teamId: String(teamId?.teamId), //  change
      datasetId,
      per: 'r'
    });

    // '=============shopset知识库详情查看-end============'
    jsonRes<DatasetItemType>(res, {
      data: {
        ...dataset,
        vectorModel: getVectorModel(dataset.vectorModel),
        agentModel: getLLMModel(dataset.agentModel),
        canWrite,
        isOwner
      }
    });
  } catch (err) {
    jsonRes(res, {
      code: 500,
      error: err
    });
  }
}
