#nprogress .bar {
  background: '#1237b3' !important; //自定义颜色
}

.textEllipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.textEllipsis3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.grecaptcha-badge {
  display: none !important;
}

.textlg {
  background: linear-gradient(to bottom right, #322659 0%, #553c9a 40%, #805ad5 80%, #9f7aea 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

span[tabindex='0'] {
  line-height: 1;
}

@keyframes zoomStopIcon {
  0% {
    transform: scale(0.8);
  }

  100% {
    transform: scale(1.2);
  }
}
