import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { withNextCors } from '@wiserag/service/common/middle/cors';
import type { SearchTestProps, SearchTestResponse } from '@/global/core/dataset/api.d';
import { connectToDatabase } from '@/service/mongo';
import { authDatasetNew } from '@wiserag/service/support/permission/auth/dataset';
import { authTeamBalance } from '@/service/support/permission/auth/bill';
import { pushGenerateVectorBill } from '@/service/support/wallet/bill/push';
import { searchDatasetData } from '@/service/core/dataset/data/controller';
import { updateApiKeyUsage } from '@wiserag/service/support/openapi/tools';
import { BillSourceEnum } from '@wiserag/global/support/wallet/bill/constants';
import { MongoDataset } from '@wiserag/service/core/dataset/schema';
import { getLLMModel } from '@/service/core/ai/model';
import { datasetSearchQueryExtension } from '@wiserag/service/core/dataset/search/utils';

export default withNextCors(async function handler(req: NextApiRequest, res: NextApiResponse<any>) {
  try {
    await connectToDatabase();
    const {
      datasetId,
      text,
      limit = 1500,
      similarity,
      searchMode,
      usingReRank,

      datasetSearchUsingExtensionQuery = false,
      datasetSearchExtensionModel,
      datasetSearchExtensionBg = ''
    } = req.body as SearchTestProps;

    if (!datasetId || !text) {
      throw new Error('缺少参数');
    }
    const start = Date.now();

    // auth dataset role
    // const { dataset, teamId, tmbId, apikey } = await authDataset({
    // req,
    // authToken: true,
    // authApiKey: true,
    // datasetId,
    // per: 'r'
    // });

    // change
    const baseInfo = await MongoDataset.findOne({ _id: datasetId });
    const teamId = String(baseInfo?.teamId);
    const { dataset, tmbId, apikey } = await authDatasetNew({
      req,
      authToken: true,
      authApiKey: true,
      datasetId,
      teamId,
      per: 'r'
    });
    // auth balance
    await authTeamBalance(teamId);

    // query extension
    const extensionModel =
      datasetSearchUsingExtensionQuery && datasetSearchExtensionModel
        ? getLLMModel(datasetSearchExtensionModel)
        : undefined;
    const { concatQueries, rewriteQuery, aiExtensionResult } = await datasetSearchQueryExtension({
      query: text,
      extensionModel,
      extensionBg: datasetSearchExtensionBg
    });

    const { searchRes, charsLength, ...result } = await searchDatasetData({
      teamId,
      reRankQuery: rewriteQuery,
      queries: concatQueries,
      model: dataset.vectorModel,
      limit: Math.min(limit, 20000),
      similarity,
      datasetIds: [datasetId],
      searchMode,
      usingReRank
    });

    // push bill
    const { total } = pushGenerateVectorBill({
      teamId,
      tmbId,
      charsLength,
      model: dataset.vectorModel,
      source: apikey ? BillSourceEnum.api : BillSourceEnum.wisegpt,

      ...(aiExtensionResult &&
        extensionModel && {
          extensionModel: extensionModel.name,
          extensionInputTokens: aiExtensionResult.inputTokens,
          extensionOutputTokens: aiExtensionResult.outputTokens
        })
    });
    if (apikey) {
      updateApiKeyUsage({
        apikey,
        usage: total
      });
    }

    jsonRes<SearchTestResponse>(res, {
      data: {
        list: searchRes,
        duration: `${((Date.now() - start) / 1000).toFixed(3)}s`,
        usingQueryExtension: !!aiExtensionResult,
        ...result
      }
    });
  } catch (err) {
    jsonRes(res, {
      code: 500,
      error: err
    });
  }
});
