import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { connectToDatabase } from '@/service/mongo';
import { MongoOpenApi } from '@wiserag/service/support/openapi/schema';
import { customAlphabet } from 'nanoid';
import type { EditApiKeyProps } from '@/global/support/openapi/api';
import { authUserNotVisitor } from '@wiserag/service/support/permission/auth/user';
import crypto from 'crypto';

// 生成一个密码学安全的随机长度
function generateSecureRandomLength(min: number, max: number) {
  const range = max - min + 1;
  const randomBuffer = crypto.randomBytes(4); // 4 bytes should be enough for the range
  const randomNumber = (randomBuffer.readUInt32BE(0) % range) + min; // Ensure the number is within the desired range
  return randomNumber;
}
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    await connectToDatabase();
    const { appId, name, limit } = req.body as EditApiKeyProps;
    const { teamId, tmbId } = await authUserNotVisitor({ req, authToken: true });

    const count = await MongoOpenApi.find({ tmbId, appId }).countDocuments();

    if (count >= 10) {
      throw new Error('最多 10 组 API 秘钥');
    }

    const secureRandomLength = generateSecureRandomLength(52, 65);
    const nanoid = customAlphabet(
      'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890',
      // Math.floor(Math.random() * 14) + 52
      secureRandomLength
    );

    // 密钥前缀待修改
    const apiKey = `${global.systemEnv?.openapiPrefix || 'wisegpt'}-${nanoid()}`;

    await MongoOpenApi.create({
      teamId,
      tmbId,
      apiKey,
      appId,
      name,
      limit
    });

    jsonRes(res, {
      data: apiKey
    });
  } catch (err) {
    jsonRes(res, {
      code: 500,
      error: err
    });
  }
}
