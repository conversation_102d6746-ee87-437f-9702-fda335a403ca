import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { connectToDatabase } from '@/service/mongo';
import { ChatItemCollectionName } from '@wiserag/service/core/chat/chatItemSchema';
import { MongoClient } from 'mongodb';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    await connectToDatabase();
    const { chatId, processingContent } = req.body;

    if (!chatId) {
      return jsonRes(res, {
        code: 400,
        message: 'chatId is required'
      });
    }

    const client = new MongoClient(process.env.MONGODB_URI as string);
    const db = client.db(process.env.MONGODB_DB);
    const collection = db.collection(ChatItemCollectionName);

    await collection.updateOne({ chatId }, { $set: { processingContent } }, { upsert: true });

    jsonRes(res, {
      message: 'Processing content saved successfully'
    });
  } catch (error) {
    jsonRes(res, {
      code: 500,
      error
    });
  }
}
