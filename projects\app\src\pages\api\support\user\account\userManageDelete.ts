import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { authCert } from '@wiserag/service/support/permission/auth/common';
import { MongoUser } from '@wiserag/service/support/user/schema';
import { connectToDatabase } from '@/service/mongo';
import { hashStr } from '@wiserag/global/common/string/tools';
import { createDefaultTeam } from '@wiserag/service/support/user/team/controller';
import { PRICE_SCALE } from '@wiserag/global/support/wallet/bill/constants';
// 删除用户接口
async function UserDelete(id: any) {
  const deletedUser = await MongoUser.findOneAndDelete({
    _id: id
  });

  if (deletedUser) {
    return deletedUser; // 返回被删除的用户对象
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse<any>) {
  try {
    await connectToDatabase();
    const { deleteId } = req.query as { deleteId: any };
    const user = await UserDelete(deleteId);

    jsonRes(res, {
      data: {
        user
      }
    });
  } catch (err) {
    jsonRes(res, {
      code: 500,
      error: err
    });
  }
}
