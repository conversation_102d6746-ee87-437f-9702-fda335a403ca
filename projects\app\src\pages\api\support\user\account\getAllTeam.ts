import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { connectToDatabase } from '@/service/mongo';

import { TeamSchema } from '@wiserag/global/support/user/team/type';
import { MongoTeam } from '@wiserag/service/support/user/team/teamSchema';

async function getAllTeam(match: Record<string, any>): Promise<TeamSchema[]> {
  const tmbs = await MongoTeam.find(match);

  if (!tmbs || tmbs.length === 0) {
    return Promise.reject('No members found');
  }

  return tmbs.map((tmb) => ({
    _id: String(tmb._id),
    ownerId: String(tmb.ownerId),
    name: tmb.name,
    avatar: tmb.avatar,
    balance: tmb.balance,
    createTime: tmb.createTime,
    maxSize: tmb.maxSize,
    limit: tmb.limit
  }));
}
export async function getAllTeamInfos() {
  return getAllTeam({});
}
export default async function handler(req: NextApiRequest, res: NextApiResponse<any>) {
  try {
    await connectToDatabase();
    const allTeams = await getAllTeamInfos();

    jsonRes(res, {
      data: allTeams
    });
  } catch (err) {
    jsonRes(res, {
      code: 500,
      error: err
    });
  }
}
