import { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { authCert } from '@wiserag/service/support/permission/auth/common';
import { MongoPrompt } from '@wiserag/service/support/user/prompt';
import { connectToDatabase } from '@/service/mongo';

// 编辑 prompt 字段接口
export default async function handler(req: NextApiRequest, res: NextApiResponse<any>) {
  try {
    await connectToDatabase();
    const { id, title, desc, aiChatQuoteTemplate, aiChatQuotePrompt } = req.body as {
      id: string;
      title: string;
      desc: string;
      aiChatQuoteTemplate: string;
      aiChatQuotePrompt: string;
    };

    const updatedPrompt = await MongoPrompt.findByIdAndUpdate(id, {
      title: title,
      desc: desc,
      aiChatQuoteTemplate: aiChatQuoteTemplate,
      aiChatQuotePrompt: aiChatQuotePrompt
    });
    jsonRes(res, {
      data: updatedPrompt
    });
  } catch (err) {
    jsonRes(res, {
      code: 500,
      error: err
    });
  }
}
