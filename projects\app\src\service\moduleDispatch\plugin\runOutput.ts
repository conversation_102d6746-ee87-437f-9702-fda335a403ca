import type { moduleDispatchResType } from '@wiserag/global/core/chat/type.d';
import type { ModuleDispatchProps } from '@wiserag/global/core/module/type.d';
import { ModuleOutputKeyEnum } from '@wiserag/global/core/module/constants';

export type PluginOutputProps = ModuleDispatchProps<{
  [key: string]: any;
}>;
export type PluginOutputResponse = {
  [ModuleOutputKeyEnum.responseData]: moduleDispatchResType;
};

export const dispatchPluginOutput = (props: PluginOutputProps): PluginOutputResponse => {
  const { params } = props;

  return {
    responseData: {
      price: 0,
      pluginOutput: params
    }
  };
};
