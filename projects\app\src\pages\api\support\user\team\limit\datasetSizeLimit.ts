import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { connectToDatabase } from '@/service/mongo';
import { authCert } from '@wiserag/service/support/permission/auth/common';
import { checkDatasetLimit } from '@wiserag/service/support/permission/limit/dataset';
import { getStandardSubPlan } from '@/service/support/wallet/sub/utils';

export default async function handler(req: NextApiRequest, res: NextApiResponse<any>) {
  try {
    await connectToDatabase();
    const { size } = req.query as {
      size: string;
    };

    // 凭证校验
    const { teamId } = await authCert({ req, authToken: true });

    if (!size) {
      return jsonRes(res);
    }

    const numberSize = Number(size);

    await checkDatasetLimit({
      teamId,
      insertLen: numberSize,
      standardPlans: getStandardSubPlan()
    });

    jsonRes(res);
  } catch (err) {
    res.status(500);
    jsonRes(res, {
      code: 500,
      error: err
    });
  }
}
