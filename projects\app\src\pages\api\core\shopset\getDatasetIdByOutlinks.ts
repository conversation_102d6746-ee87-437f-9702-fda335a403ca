import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { connectToDatabase } from '@/service/mongo';
import { MongoDatasetOutLink } from '@wiserag/service/core/dataset/outlink/schema';

export default async function handler(req: NextApiRequest, res: NextApiResponse<any>) {
  try {
    await connectToDatabase();
    const { _id, userId } = req.body as { _id?: string; userId?: string };
    const data = await MongoDatasetOutLink.findOne({ datasetId: _id, userId: userId });
    jsonRes(res, {
      data: data
    });
  } catch (err) {
    jsonRes(res, {
      code: 500,
      error: err
    });
  }
}
