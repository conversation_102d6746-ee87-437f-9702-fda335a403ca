import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import axios from 'axios';
import { OAuthEnum } from '@wiserag/global/support/user/constant';
import type {
  AudioSpeechModelType,
  LLMModelItemType,
  ReRankModelItemType,
  VectorModelItemType,
  WhisperModelType
} from '@wiserag/global/core/ai/model.d';
import { InitDateResponse } from '@/global/common/api/systemRes';
import { WiseGPTFeConfigsType } from '@wiserag/global/common/system/types';
import { SubPlanType } from '@wiserag/global/support/wallet/sub/type';
import { AppSimpleEditConfigTemplateType } from '@wiserag/global/core/app/type';

type LoginStoreType = { provider: `${OAuthEnum}`; lastRoute: string; state: string };

type State = {
  initd: boolean;
  setInitd: () => void;
  lastRoute: string;
  setLastRoute: (e: string) => void;
  loginStore?: LoginStoreType;
  setLoginStore: (e: LoginStoreType) => void;
  loading: boolean;
  setLoading: (val: boolean) => null;
  screenWidth: number;
  setScreenWidth: (val: number) => void;
  isPc?: boolean;
  initIsPc(val: boolean): void;
  gitStar: number;
  loadGitStar: () => Promise<void>;

  feConfigs: WiseGPTFeConfigsType;
  subPlans?: SubPlanType;
  systemVersion: string;
  llmModelList: LLMModelItemType[];
  datasetModelList: LLMModelItemType[];
  vectorModelList: VectorModelItemType[];
  audioSpeechModelList: AudioSpeechModelType[];
  reRankModelList: ReRankModelItemType[];
  whisperModel?: WhisperModelType;
  simpleModeTemplates: AppSimpleEditConfigTemplateType[];
  initStaticData: (e: InitDateResponse) => void;
};

export const useSystemStore = create<State>()(
  devtools(
    persist(
      immer((set, get) => ({
        initd: false,
        setInitd() {
          set((state) => {
            state.initd = true;
          });
        },
        lastRoute: '/app/list',
        setLastRoute(e) {
          set((state) => {
            state.lastRoute = e;
          });
        },
        loginStore: undefined,
        setLoginStore(e) {
          set((state) => {
            state.loginStore = e;
          });
        },
        loading: false,
        setLoading: (val: boolean) => {
          set((state) => {
            state.loading = val;
          });
          return null;
        },
        screenWidth: 600,
        setScreenWidth(val: number) {
          set((state) => {
            state.screenWidth = val;
            state.isPc = val < 900 ? false : true;
          });
        },
        isPc: undefined,
        initIsPc(val: boolean) {
          if (get().isPc !== undefined) return;

          set((state) => {
            state.isPc = val;
          });
        },
        gitStar: 9300,
        async loadGitStar() {
          try {
            const { data: git } = await axios.get('https://api.github.com/repos/labring/FastGPT');

            set((state) => {
              state.gitStar = git.stargazers_count;
            });
          } catch (error) {}
        },

        feConfigs: {},
        subPlans: undefined,
        systemVersion: '0.0.0',
        llmModelList: [],
        datasetModelList: [],
        vectorModelList: [],
        audioSpeechModelList: [],
        reRankModelList: [],
        whisperModel: undefined,
        simpleModeTemplates: [],
        initStaticData(res) {
          set((state) => {
            state.feConfigs = res.feConfigs || {};
            state.subPlans = res.subPlans;
            state.systemVersion = res.systemVersion;

            state.llmModelList = res.llmModels ?? state.llmModelList;
            state.datasetModelList = state.llmModelList.filter((item) => item.datasetProcess);
            state.vectorModelList = res.vectorModels ?? state.vectorModelList;
            state.audioSpeechModelList = res.audioSpeechModels ?? state.audioSpeechModelList;
            state.reRankModelList = res.reRankModels ?? state.reRankModelList;
            state.whisperModel = res.whisperModel;

            state.simpleModeTemplates = res.simpleModeTemplates;
          });
        }
      })),
      {
        name: 'globalStore',
        partialize: (state) => ({
          loginStore: state.loginStore
        })
      }
    )
  )
);
