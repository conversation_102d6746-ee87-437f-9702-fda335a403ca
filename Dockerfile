# --------- install dependence -----------
FROM node:18.17-alpine AS mainDeps
WORKDIR /app

ARG name
ARG proxy

# 首先设置镜像源（无论是否有 proxy 参数都设置）
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 安装基础依赖（如果 canvas 编译失败，可以先移除 canvas 相关依赖）
RUN apk add --no-cache libc6-compat

RUN npm install -g pnpm@8.6.0
# if proxy exists, set proxy
RUN [ -z "$proxy" ] || pnpm config set registry https://registry.npmmirror.com
RUN pnpm config set registry https://registry.npmmirror.com

# copy packages and one project
COPY pnpm-lock.yaml pnpm-workspace.yaml ./
COPY ./packages ./packages
COPY ./projects/$name/package.json ./projects/$name/package.json

RUN [ -f pnpm-lock.yaml ] || (echo "Lockfile not found." && exit 1)
RUN pnpm config get registry
RUN pnpm i

# --------- install dependence -----------
FROM node:18.17-alpine AS workerDeps
WORKDIR /app

ARG proxy

RUN [ -z "$proxy" ] || sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories
RUN apk add --no-cache libc6-compat && npm install -g pnpm@8.6.0
# if proxy exists, set proxy
RUN [ -z "$proxy" ] || pnpm config set registry https://registry.npmmirror.com
RUN pnpm config set registry https://registry.npmmirror.com
COPY ./worker /app/worker
RUN cd /app/worker && pnpm i --production --ignore-workspace

# --------- builder -----------
FROM node:18.17-alpine AS builder
WORKDIR /app

ARG name
ARG proxy

# copy common node_modules and one project node_modules
COPY package.json pnpm-workspace.yaml ./
COPY --from=mainDeps /app/node_modules ./node_modules
COPY --from=mainDeps /app/packages ./packages
COPY ./projects/$name ./projects/$name
COPY --from=mainDeps /app/projects/$name/node_modules ./projects/$name/node_modules

RUN [ -z "$proxy" ] || sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories
#  配置nmp代理来提高速度，如设置淘宝镜像
RUN npm config set registry https://registry.npmmirror.com

# 查看配置是否成功
RUN npm config get registry
# step 37gi
RUN apk add --no-cache libc6-compat && npm install -g pnpm@8.6.0
RUN ls /app
#RUN pnpm --filter=$name build && echo "Build successful!"
RUN pnpm run --parallel build && echo "Build successful!"
RUN ls ./projects/$name/.next/
RUN test -d ./projects/$name/.next/standalone || (echo "/app/projects/$name/.next/standalone directory not found" && exit 1)

# --------- runner -----------
FROM node:18.17-alpine AS runner
WORKDIR /app

ARG name
ARG proxy

# create user and use it
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

RUN [ -z "$proxy" ] || sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories
RUN apk add --no-cache curl ca-certificates \
  && update-ca-certificates

# copy running files
COPY --from=builder /app/projects/$name/public /app/projects/$name/public
COPY --from=builder /app/projects/$name/next.config.js /app/projects/$name/next.config.js
COPY --from=builder --chown=nextjs:nodejs /app/projects/$name/.next/standalone /app/
COPY --from=builder --chown=nextjs:nodejs /app/projects/$name/.next/static /app/projects/$name/.next/static
# copy package.json to version file
COPY --from=builder /app/projects/$name/package.json ./package.json 
# copy woker
COPY --from=workerDeps /app/worker /app/worker
# copy config
COPY ./projects/$name/data /app/data
RUN chown -R nextjs:nodejs /app/data
RUN chmod -R u+rw /app/data

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1
ENV PORT=3000

EXPOSE 3000

USER nextjs

ENV serverPath=./projects/$name/server.js

ENTRYPOINT ["sh","-c","node ${serverPath}"]
