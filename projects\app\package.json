{"name": "wiserag", "version": "1.2.4", "private": false, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky install || true"}, "dependencies": {"@chakra-ui/anatomy": "^2.2.1", "@chakra-ui/icons": "^2.1.1", "@chakra-ui/next-js": "^2.1.5", "@chakra-ui/react": "^2.8.1", "@chakra-ui/styled-system": "^2.9.1", "@chakra-ui/system": "^2.6.1", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@wiserag/global": "workspace:*", "@wiserag/plugins": "workspace:*", "@wiserag/service": "workspace:*", "@wiserag/web": "workspace:*", "@fortaine/fetch-event-source": "^3.0.6", "@node-rs/jieba": "^1.7.2", "@tanstack/react-query": "^4.24.10", "@types/nprogress": "^0.2.0", "axios": "^1.5.1", "base-64": "^1.0.0", "date-fns": "^2.30.0", "dayjs": "^1.11.7", "docx-preview": "^0.3.2", "echarts": "^5.4.1", "echarts-gl": "^2.0.9", "formidable": "^2.1.1", "framer-motion": "^9.0.6", "hyperdown": "^2.4.29", "i18next": "^22.5.1", "immer": "^9.0.19", "jschardet": "^3.0.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mermaid": "^10.2.3", "minio": "^8.0.0", "nanoid": "^4.0.1", "next": "13.5.2", "next-i18next": "^13.3.0", "nprogress": "^0.2.0", "react": "18.2.0", "react-day-picker": "^8.7.1", "react-doc-viewer": "^0.1.11", "react-dom": "18.2.0", "react-file-viewer": "^1.2.1", "react-hook-form": "^7.43.1", "react-i18next": "^12.3.1", "react-markdown": "^8.0.7", "react-office-viewer": "^1.0.4", "react-paginate": "^8.2.0", "react-pptx": "^2.20.1", "react-syntax-highlighter": "^15.5.0", "reactflow": "^11.7.4", "rehype-katex": "^6.0.2", "remark-breaks": "^3.0.3", "remark-gfm": "^3.0.1", "remark-math": "^5.1.1", "request-ip": "^3.3.0", "sass": "^1.58.3", "zustand": "^4.3.5", "xlsx": "^0.18.5", "mongodb": "^4.13.0", "base64-js": "^1.5.1"}, "devDependencies": {"@babel/core": "^7.22.5", "@babel/preset-env": "^7.22.5", "@babel/preset-react": "^7.22.5", "babel-loader": "^8.3.0", "@svgr/webpack": "^6.5.1", "@types/formidable": "^2.0.5", "@types/js-cookie": "^3.0.3", "@types/jsonwebtoken": "^9.0.3", "@types/lodash": "^4.14.191", "@types/node": "^20.8.5", "@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@types/react-syntax-highlighter": "^15.5.6", "@types/request-ip": "^0.0.37", "eslint": "8.34.0", "eslint-config-next": "13.1.6", "typescript": "4.9.5"}}