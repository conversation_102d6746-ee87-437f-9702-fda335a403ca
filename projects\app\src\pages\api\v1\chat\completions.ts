import type { NextApiRequest, NextApiResponse } from 'next';
import { authApp, authAppNew } from '@wiserag/service/support/permission/auth/app';
import { authCert } from '@wiserag/service/support/permission/auth/common';
import { sseErrRes, jsonRes } from '@wiserag/service/common/response';
import { addLog } from '@wiserag/service/common/system/log';
import { withNextCors } from '@wiserag/service/common/middle/cors';
import { ChatRoleEnum, ChatSourceEnum } from '@wiserag/global/core/chat/constants';
import { sseResponseEventEnum } from '@wiserag/service/common/response/constant';
import { dispatchModules } from '@/service/moduleDispatch';
import type { ChatCompletionCreateParams } from '@wiserag/global/core/ai/type.d';
import type { ChatMessageItemType } from '@wiserag/global/core/ai/type.d';
import { gptMessage2ChatType, textAdaptGptResponse } from '@/utils/adapt';
import { getChatItems } from '@wiserag/service/core/chat/controller';
import { saveChat } from '@/service/utils/chat/saveChat';
import { responseWrite } from '@wiserag/service/common/response';
import { pushChatBill } from '@/service/support/wallet/bill/push';
import { authOutLinkChatStart } from '@/service/support/permission/auth/outLink';
import { pushResult2Remote, updateOutLinkUsage } from '@wiserag/service/support/outLink/tools';
import requestIp from 'request-ip';
import { getBillSourceByAuthType } from '@wiserag/global/support/wallet/bill/tools';

import { selectShareResponse } from '@/utils/service/core/chat';
import { updateApiKeyUsage } from '@wiserag/service/support/openapi/tools';
import { connectToDatabase } from '@/service/mongo';
import { getUserAndAuthBalance } from '@wiserag/service/support/user/controller';
import { AuthUserTypeEnum } from '@wiserag/global/support/permission/constant';
import { MongoApp } from '@wiserag/service/core/app/schema';
import { autChatCrud } from '@/service/support/permission/auth/chat';

/**
 * `FastGptWebChatProps` 类型定义了带有可选 `chatId` 和 `appId` 属性的网络聊天组件的 props。
 * @property {string} chatId - “FastGptWebChatProps”类型中的“chatId”属性用于指定聊天的 ID。它可以具有三个可能的值：
 * @property {string} appId - `appId` 属性是一个字符串，表示用于聊天的应用程序的 ID。
 */
type FastGptWebChatProps = {
  chatId?: string; // undefined: nonuse history, '': new chat, 'xxxxx': use history
  appId?: string;
};

/**
 * “FastGptShareChatProps”类型定义了在快速 GPT 系统中共享聊天信息的属性。
 * @property {string} shareId - “FastGptShareChatProps”类型中的“shareId”属性是一个字符串类型，表示与共享关联的
 * ID。它是可选的，由“？”符号表示，这意味着它可以是字符串值或未定义。
 * @property {string} outLinkUid -
 * “FastGptShareChatProps”类型中的“outLinkUid”属性表示与外部链接关联的唯一标识符。它是一个可选属性，由“？”符号表示，这意味着在使用此类型时可以提供也可以不提供。
 */
type FastGptShareChatProps = {
  shareId?: string;
  outLinkUid?: string;
};

export type Props = ChatCompletionCreateParams &
  FastGptWebChatProps &
  FastGptShareChatProps & {
    messages: ChatMessageItemType[];
    stream?: boolean;
    detail?: boolean;
    variables: Record<string, any>;
  };
/**
 * TypeScript 中的 ChatResponseType 类型定义一个对象，该对象具有字符串类型的 newChatId 属性和数字类型的可选 quoteLen 属性。
 * @property {string} newChatId - “ChatResponseType”类型中的“newChatId”属性表示新聊天的唯一标识符。它是必填字段，并且必须是字符串数据类型。
 * @property {number} quoteLen -
 * “ChatResponseType”类型中的“quoteLen”属性是一个可选属性，表示聊天响应中引用的长度。它不需要出现在每个“ChatResponseType”对象中，如属性名称后面的“?”符号所示。如果包括在内，
 */

export type ChatResponseType = {
  newChatId: string;
  quoteLen?: number;
};
/* 上面的代码是 TypeScript 文件中的异步函数处理程序，充当 API 端点。以下是该代码正在执行的操作的详细说明： */

export default withNextCors(async function handler(req: NextApiRequest, res: NextApiResponse) {
  res.on('close', () => {
    res.end();
  });
  res.on('error', () => {
    console.log('error: ', 'request error');
    res.end();
  });

  const {
    chatId,
    appId,
    shareId,
    outLinkUid,
    stream = false,
    detail = false,
    messages = [],
    variables = {}
  } = req.body as Props;

  try {
    const originIp = requestIp.getClientIp(req);
    console.log('originIp:', originIp);

    await connectToDatabase();
    // body data check
    if (!messages) {
      throw new Error('Prams Error');
    }
    if (!Array.isArray(messages)) {
      throw new Error('messages is not array');
    }
    if (messages.length === 0) {
      throw new Error('messages is empty');
    }

    let startTime = Date.now();

    /* 您提供的代码片段正在处理 API 端点中收到的聊天消息。以下是代码的作用： */
    const chatMessages = gptMessage2ChatType(messages);
    if (chatMessages[chatMessages.length - 1].obj !== ChatRoleEnum.Human) {
      chatMessages.pop();
    }

    // 拿出 user question
    const question = chatMessages.pop();
    if (!question) {
      throw new Error('Question is empty');
    }

    /* auth app permission */
    const { user, app, responseDetail, authType, apikey, canWrite, uid } = await (async () => {
      // 判断是否是分享的请求页面
      if (shareId && outLinkUid) {
        const { user, appId, authType, responseDetail, uid } = await authOutLinkChatStart({
          shareId,
          ip: originIp,
          outLinkUid,
          question: question.value
        });
        const app = await MongoApp.findById(appId);

        if (!app) {
          return Promise.reject('app is empty');
        }

        return {
          user,
          app,
          responseDetail,
          apikey: '',
          authType,
          canWrite: false,
          uid
        };
      }
      // 异步验证用户身份
      const {
        appId: apiKeyAppId,
        tmbId,
        authType,
        apikey
      } = await authCert({
        req,
        authToken: true,
        authApiKey: true
      });
      //检索用户详细信息并检查用户是否获得授权以及是否具有最低余额
      const user = await getUserAndAuthBalance({
        tmbId,
        minBalance: 0
      });
      // openapi key
      if (authType === AuthUserTypeEnum.apikey) {
        if (!apiKeyAppId) {
          return Promise.reject(
            'Key is error. You need to use the app key rather than the account key.'
          );
        }
        const app = await MongoApp.findById(apiKeyAppId);

        if (!app) {
          return Promise.reject('app is empty');
        }

        return {
          user,
          app,
          responseDetail: detail,
          apikey,
          authType,
          canWrite: true
        };
      }
      // token auth
      if (!appId) {
        return Promise.reject('appId is empty');
      }
      // const { app, canWrite } = await authApp({
      //   req,
      //   authToken: true,
      //   appId,
      //   per: 'r'
      // });
      // change
      const { app, canWrite } = await authAppNew({
        req,
        authToken: true,
        appId,
        per: 'r'
      });

      return {
        user,
        app,
        responseDetail: detail,
        apikey,
        authType,
        canWrite: canWrite || false
      };
    })();
    console.log('////////////++++', user);
    // auth chat permission
    await autChatCrud({
      req,
      authToken: true,
      authApiKey: true,
      appId: app._id,
      chatId,
      shareId,
      outLinkUid,
      per: 'w'
    });

    // get and concat history 拼接历史
    const { history } = await getChatItems({
      appId: app._id,
      chatId,
      limit: 30,
      field: `dataId obj value`
    });
    const concatHistories = history.concat(chatMessages);
    const responseChatItemId: string | undefined = messages[messages.length - 1].dataId;

    /* start flow controller */
    // 启动编排控制
    // 任务执行
    const { responseData, answerText } = await dispatchModules({
      res,
      mode: 'chat',
      user,
      teamId: String(user.team.teamId),
      tmbId: String(user.team.tmbId),
      appId: String(app._id),
      chatId,
      responseChatItemId,
      modules: app.modules,
      variables,
      histories: concatHistories,
      startParams: {
        userChatInput: question.value
      },
      stream,
      detail
    });

    // save chat
    //保存 聊天
    if (chatId) {
      await saveChat({
        chatId,
        appId: app._id,
        userId: user._id,
        teamId: user.team.teamId,
        tmbId: user.team.tmbId,
        variables,
        updateUseTime: !shareId && String(user.team.tmbId) === String(app.tmbId), // owner update use time
        shareId,
        outLinkUid: uid,
        source: (() => {
          if (shareId) {
            return ChatSourceEnum.share;
          }
          if (authType === 'apikey') {
            return ChatSourceEnum.api;
          }
          return ChatSourceEnum.online;
        })(),
        content: [
          question,
          {
            dataId: responseChatItemId,
            obj: ChatRoleEnum.AI,
            value: answerText,
            responseData
          }
        ],
        metadata: {
          originIp
        }
      });
    }

    addLog.info(`【完成运行】completions running time: ${(Date.now() - startTime) / 1000}s`);

    /* select fe response field */
    const feResponseData = canWrite ? responseData : selectShareResponse({ responseData });

    if (stream) {
      //如果是流式
      responseWrite({
        res,
        event: detail ? sseResponseEventEnum.answer : undefined,
        data: textAdaptGptResponse({
          text: null,
          finish_reason: 'stop'
        })
      });
      responseWrite({
        res,
        event: detail ? sseResponseEventEnum.answer : undefined,
        data: '[DONE]'
      });

      if (responseDetail && detail) {
        responseWrite({
          res,
          event: sseResponseEventEnum.appStreamResponse,
          data: JSON.stringify(feResponseData)
        });
      }

      res.end();
    } else {
      //如果不是流式
      res.json({
        ...(detail ? { responseData: feResponseData } : {}),
        id: chatId || '',
        model: '',
        usage: { prompt_tokens: 1, completion_tokens: 1, total_tokens: 1 },
        choices: [
          {
            message: { role: 'assistant', content: answerText },
            finish_reason: 'stop',
            index: 0
          }
        ]
      });
    }

    // add record
    // 账单记录
    const { total } = pushChatBill({
      appName: app.name,
      appId: app._id,
      teamId: user.team.teamId,
      tmbId: user.team.tmbId,
      source: getBillSourceByAuthType({ shareId, authType }),
      response: responseData
    });

    if (shareId) {
      pushResult2Remote({ outLinkUid, shareId, responseData });
      updateOutLinkUsage({
        shareId,
        total
      });
    }
    if (apikey) {
      updateApiKeyUsage({
        apikey,
        usage: total
      });
    }
  } catch (err: any) {
    if (stream) {
      sseErrRes(res, err);
      res.end();
    } else {
      jsonRes(res, {
        code: 500,
        error: err
      });
    }
  }
});

/* `export const config = { api: { responseLimit: '20mb' } };` 语句正在配置端点的 API 响应限制。在本例中，它将响应限制设置为 20 兆字节
(20mb)。此配置可确保 API 端点可以处理最大 20mb 的响应。这对于处理 API 响应中的大型响应或数据负载非常有用。 */
export const config = {
  api: {
    responseLimit: '20mb'
  }
};
