import React, { useCallback, useEffect, useState } from 'react';
import {
  Box,
  Grid,
  Flex,
  IconButton,
  Button,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  Input,
  ModalFooter
} from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { useQuery } from '@tanstack/react-query';
import { AddIcon } from '@chakra-ui/icons';
import { delModelById } from '@/web/core/app/api';
import { useToast } from '@wiserag/web/hooks/useToast';
import { useConfirm } from '@/web/common/hooks/useConfirm';
import { serviceSideProps } from '@/web/common/utils/i18n';
import { useTranslation } from 'next-i18next';

import MyIcon from '@wiserag/web/components/common/Icon';
import PageContainer from '@/components/PageContainer';
import Avatar from '@/components/Avatar';
import MyTooltip from '@/components/MyTooltip';
import CreateModal from './component/CreateModal';
import { useAppStore } from '@/web/core/appShop/store/useAppStore';
import PermissionIconText from '@/components/support/permission/IconText';
import { useUserStore } from '@/web/support/user/useUserStore';
import MyMenu from '@/components/MyMenu';
// import { appShoppublishApplication } from '@/web/core/appShop/api';
import { bindShardApp } from '@/web/core/appShop/api';

const MyApps = () => {
  const { toast } = useToast();
  const { t } = useTranslation();
  const router = useRouter();
  const { userInfo } = useUserStore();
  const { myApps, loadMyApps } = useAppStore();
  const { openConfirm, ConfirmModal } = useConfirm({
    title: '删除提示',
    content: '确认删除该应用所有信息？'
  });
  const {
    isOpen: isOpenCreateModal,
    onOpen: onOpenCreateModal,
    onClose: onCloseCreateModal
  } = useDisclosure();

  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isOpenShareId, onOpen: onOpenShareId, onClose: onCloseShareId } = useDisclosure();
  const [appId, setappId] = useState('');
  const [sharedId, setsharedId] = useState('');
  const [shareIdData, setshareIdData] = useState('');
  const shareIdDataChange = (e: { target: { value: React.SetStateAction<string> } }) => {
    setshareIdData(e.target.value);
  };
  const userId = String(userInfo?.team.userId);
  const { data: bindShardAppData = [], refetch: bindShardAppRefetch } = useQuery<any>(
    ['bindShardApp', appId, userId, sharedId],
    () => bindShardApp(appId, userId, sharedId),
    {
      enabled: false, // 初始时不执行查询
      onSuccess(data) {
        if (data == '绑定成功') {
          toast({
            title: data,
            status: 'success'
          });
          onClose();
          onCloseShareId();
          loadMyApps();
        } else {
          toast({
            title: data,
            status: 'error'
          });
          onClose();
          onCloseShareId();
        }
      },
      onError(error) {
        toast({
          title: '绑定失败',
          status: 'error'
        });
        onClose();
        onCloseShareId();
      }
    }
  );
  const publishApplicationAddButton = async () => {
    try {
      //调用订阅接口
      if (sharedId == 'FREE') {
        // 如果是free则直接调用那个绑定接口 sharedId传入FREE
        setsharedId('FREE');
        bindShardAppRefetch();
      } else {
        //如果是非free 则弹出对话框 让他输入授权码 然后点击确认后调用绑定逻辑
        onOpenShareId();
      }
      onClose(); // 关闭弹窗
      // 确保数据已刷新
    } catch (error) {
      console.error(error);
    }
  };
  useEffect(() => {
    setsharedId(shareIdData);
  }, [shareIdData]);

  async function onclickShareId() {
    setsharedId(shareIdData);
    bindShardAppRefetch();
  }
  // 执行订阅操作
  const publish = (payload: { app: any }) => {
    setappId(payload.app._id);
    setsharedId(payload.app.appPublishType);
    onOpen();
  };
  /* 点击删除 */
  const onclickDelApp = useCallback(
    async (id: string) => {
      try {
        await delModelById(id);
        toast({
          title: '删除成功',
          status: 'success'
        });
        loadMyApps(true);
      } catch (err: any) {
        toast({
          title: err?.message || '删除失败',
          status: 'error'
        });
      }
    },
    [toast, loadMyApps]
  );

  /* 加载模型 */
  const { isFetching } = useQuery(['loadApps'], () => loadMyApps(true), {
    refetchOnMount: true
  });

  return (
    <PageContainer isLoading={isFetching} insertProps={{ px: [5, '48px'] }}>
      <Flex pt={[4, '30px']} alignItems={'center'} justifyContent={'space-between'}>
        <Flex flex={1} alignItems={'center'}>
          <Box className="textlg" letterSpacing={1} fontSize={['20px', '24px']} fontWeight={'bold'}>
            {/* {t('app.My Apps')} */}
            应用商店
          </Box>
        </Flex>
        {/* {userInfo?.team?.canWrite && (
          <Button leftIcon={<AddIcon />} variant={'primaryOutline'} onClick={onOpenCreateModal}>
            {t('common.New Create')}
          </Button>
        )} */}
      </Flex>
      <Grid
        py={[4, 6]}
        gridTemplateColumns={['1fr', 'repeat(2,1fr)', 'repeat(3,1fr)', 'repeat(4,1fr)']}
        gridGap={5}
      >
        {myApps.map((app) => (
          <MyTooltip
            key={app._id}
            // label={userInfo?.team.canWrite ? t('app.To Settings') : t('app.To Chat')}
          >
            <Box
              lineHeight={1.5}
              h={'100%'}
              py={3}
              px={5}
              cursor={'pointer'}
              borderWidth={'1.5px'}
              borderColor={'borderColor.low'}
              // bg={app?.shareIdisUsed && userInfo?.username !== 'root' ? '#e8ebf0' : 'white'}
              bg={
                app?.appPublishType == 'FREE'
                  ? '#F0FFF0'
                  : app?.shareIdisUsed && userInfo?.username !== 'root'
                    ? '#e8ebf0'
                    : 'white'
              }
              borderRadius={'md'}
              userSelect={'none'}
              position={'relative'}
              display={'flex'}
              flexDirection={'column'}
              _hover={
                app?.shareIdisUsed
                  ? undefined
                  : {
                      borderColor: 'primary.300',
                      boxShadow: '1.5',
                      '& .delete': {
                        display: 'flex'
                      },
                      '& .chat': {
                        display: 'flex'
                      }
                    }
              }
              onClick={() => {
                return;
                // if (!app?.shareIdisUsed) {
                //   // 如果role为visitor，不进行任何操作
                //   toast({
                //     status: 'warning',
                //     title: '您目前是访客权限，无法编辑应用，使用本应用可进入"工作台"'
                //   });
                //   return;
                // }
                // if (userInfo?.team.canWrite) {
                //   router.push(`/app/detail?appId=${app._id}`);
                // } else {
                //   router.push(`/chat?appId=${app._id}`);
                // }
              }}
            >
              {userInfo?.username !== 'root' && !app?.shareIdisUsed && (
                <Box
                  position={'absolute'}
                  top={3}
                  right={3}
                  borderRadius={'md'}
                  _hover={{
                    color: 'primary.500',
                    '& .icon': {
                      bg: 'myGray.100'
                    }
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                >
                  <MyMenu
                    width={120}
                    Button={
                      <Box w={'22px'} h={'22px'}>
                        <MyIcon
                          className="icon"
                          name={'more'}
                          h={'16px'}
                          w={'16px'}
                          px={1}
                          py={1}
                          borderRadius={'md'}
                          cursor={'pointer'}
                        />
                      </Box>
                    }
                    menuList={[
                      ...(userInfo?.username !== 'root'
                        ? [
                            {
                              label: (
                                <Flex alignItems={'center'}>
                                  <MyIcon name={'export'} w={'14px'} mr={2} />
                                  订阅
                                </Flex>
                              ),
                              onClick: () => {
                                publish({ app: app });
                              }
                            }
                          ]
                        : [])
                    ]}
                  />
                </Box>
              )}
              <Flex alignItems={'center'} h={'38px'}>
                <Avatar src={app.avatar} borderRadius={'md'} w={'28px'} />
                <Box ml={3}>{app.name}</Box>
                {/* {app.isOwner && userInfo?.team.canWrite && (
                    <IconButton
                      className="delete"
                      position={'absolute'}
                      top={4}
                      right={4}
                      size={'xsSquare'}
                      variant={'whiteDanger'}
                      icon={<MyIcon name={'delete'} w={'14px'} />}
                      aria-label={'delete'}
                      display={['', 'none']}
                      onClick={(e) => {
                        e.stopPropagation();
                        openConfirm(() => onclickDelApp(app._id))();
                      }}
                    />
                  )} */}
              </Flex>
              <Box
                flex={1}
                className={'textEllipsis3'}
                py={2}
                wordBreak={'break-all'}
                fontSize={'sm'}
                color={'myGray.600'}
              >
                {app.intro || '这个应用还没写介绍~'}
              </Box>
              <Flex h={'34px'} alignItems={'flex-end'}>
                <Box flex={1}>
                  <PermissionIconText permission={app.permission} color={'myGray.600'} />
                </Box>
                {/* {userInfo?.team.canWrite && (
                  <IconButton
                    className="chat"
                    size={'xsSquare'}
                    variant={'whitePrimary'}
                    icon={
                      <MyTooltip label={'去聊天'}>
                        <MyIcon name={'core/chat/chatLight'} w={'14px'} />
                      </MyTooltip>
                    }
                    aria-label={'chat'}
                    display={['', 'none']}
                    onClick={(e) => {
                      e.stopPropagation();
                      router.push(`/chat?appId=${app._id}`);
                    }}
                  />
                )} */}
              </Flex>
            </Box>
          </MyTooltip>
        ))}
      </Grid>
      {myApps.length === 0 && (
        <Flex mt={'35vh'} flexDirection={'column'} alignItems={'center'}>
          <MyIcon name="empty" w={'48px'} h={'48px'} color={'transparent'} />
          <Box mt={2} color={'myGray.500'}>
            还没有应用，快去创建一个吧！
          </Box>
        </Flex>
      )}
      <ConfirmModal />
      {isOpenCreateModal && (
        <CreateModal onClose={onCloseCreateModal} onSuccess={() => loadMyApps(true)} />
      )}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>订阅数据库</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            是否订阅？
            {/* <Lorem count={2} /> */}
            {/* <Flex mt={6} alignItems={'center'} w={['85%', '300px']}>
              <Box flex={'0 0 100px'}>数据库名称:&nbsp;</Box>
              <Box flex={1}>
                <Input placeholder="请输入数据库名称" value={name} onChange={nameChange} />
              </Box>
            </Flex>
            <Flex mt={6} alignItems={'center'} w={['85%', '300px']}>
              <Box flex={'0 0 100px'}>授权码:&nbsp;</Box>
              <Box flex={1}>
                <Input placeholder="请输入授权码" value={shareId} onChange={shareIdChange} />
              </Box>
            </Flex> */}
          </ModalBody>

          <ModalFooter>
            <Button colorScheme="blue" variant="outline" mr={3} onClick={onClose}>
              取消
            </Button>
            <Button colorScheme="blue" onClick={publishApplicationAddButton}>
              确认
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
      <Modal isOpen={isOpenShareId} onClose={onCloseShareId}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>授权码生成</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {/* <Lorem count={2} /> */}
            <Flex mt={6} alignItems={'center'} w={['85%', '300px']}>
              <Box flex={'0 0 60px'}>授权码:&nbsp;</Box>
              <Box flex={1}>
                <Input
                  placeholder="请输入授权码"
                  value={shareIdData}
                  onChange={shareIdDataChange}
                />
              </Box>
            </Flex>
          </ModalBody>

          <ModalFooter>
            <Button colorScheme="blue" variant="outline" mr={3} onClick={onCloseShareId}>
              取消
            </Button>
            <Button colorScheme="blue" onClick={onclickShareId}>
              确认
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </PageContainer>
  );
};

export async function getServerSideProps(content: any) {
  return {
    props: {
      ...(await serviceSideProps(content))
    }
  };
}

export default MyApps;
