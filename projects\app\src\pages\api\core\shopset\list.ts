import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { connectToDatabase } from '@/service/mongo';
import type { StoreWithDatasetSchema } from '@wiserag/global/core/dataset/type.d';
import { DatasetTypeEnum } from '@wiserag/global/core/dataset/constants';
import { authUserRole } from '@wiserag/service/support/permission/auth/user';
import { getVectorModel } from '@/service/core/ai/model';
import { TeamItemType, TeamMemberWithTeamSchema } from '@wiserag/global/support/user/team/type';
import { TeamMemberRoleEnum, notLeaveStatus } from '@wiserag/global/support/user/team/constant';
import { MongoStore } from '@wiserag/service/core/subs/schema';
import { MongoDatasetOutLink } from '@wiserag/service/core/dataset/outlink/schema';
import { PermissionTypeEnum } from '@wiserag/global/support/permission/constant';
import { MongoTeamMember } from '@wiserag/service/support/user/team/teamMemberSchema';
import { Types } from '@wiserag/service/common/mongo';

/*
   AQ change
   基于用户stores表获取可订阅的数据库
   */
export async function getSubscriptionDataSets({ userId }: { userId: string }) {
  // 获取用户已经订阅的数据合集
  const subDatasetInfos = await MongoDatasetOutLink.find({ userId, status: 1 }, { datasetId: 1 });
  const isSubscriptedDataSetIds = subDatasetInfos.map((item) => {
    return String(item.datasetId);
  });

  // '=======筛选掉同组内成员发布的订阅库=========='
  /**
   * 订阅库商店用户查看时，若发布的订阅知识库是与查看用户同组则不显示
   *
   * 1. 获取与当前用户存在同组关系的成员组创建者userId
   * 2. 筛选掉1步骤作为发布者的订阅知识库数据
   */

  //start-获取当前用户所有角色的团队列表信息(userId: { $nin: [....] })
  const tmbs = (await MongoTeamMember.find({
    userId: new Types.ObjectId(userId),
    status: notLeaveStatus
  }).populate('teamId')) as TeamMemberWithTeamSchema[];
  if (!tmbs) {
    return Promise.reject('member not exist');
  }
  const sameGroupUserIdsBase = tmbs.map((tmb) => {
    return String(tmb.teamId.ownerId);
  });
  // 还需要加入若创建者自己查看商店是可以看到自己发布的订阅库信息，从sameGroupUserIds移除当前userId数据
  const sameGroupUserIds = sameGroupUserIdsBase.filter((id) => id !== String(userId));
  // end

  const datasets = (await MongoStore.find({ userId: { $nin: sameGroupUserIds } }).populate(
    'datasetId'
  )) as StoreWithDatasetSchema[];

  if (!datasets) {
    console.log('无可用订阅知识库！！！');
    return [];
  }

  return datasets.map((item) => ({
    _id: item.datasetId._id,
    parentId: item.datasetId.parentId,
    avatar: item.datasetId.avatar,
    name: item.datasetId.name,
    intro: item.datasetId.intro,
    type: DatasetTypeEnum.sharedDataset,
    permission: PermissionTypeEnum.zw, // 订阅知识库时此处设置为空，不显示团队还是私有,原始值为item.datasetId.permission
    canWrite: true, // 订阅知识库时设置true
    isOwner: true, // 订阅知识库时设置true
    vectorModel: getVectorModel(item.datasetId.vectorModel),
    canPublish: false,
    shareIdisUsed: isSubscriptedDataSetIds.includes(String(item.datasetId._id)) // 该数据是否被用户订阅，若为true则前端置灰，不可操作
  }));
}

export default async function handler(req: NextApiRequest, res: NextApiResponse<any>) {
  try {
    await connectToDatabase();
    const { parentId, type } = req.query as { parentId?: string; type?: `${DatasetTypeEnum}` };
    // 凭证校验
    const { userId, tmbId, teamOwner } = await authUserRole({
      req,
      authToken: true,
      authApiKey: true
    });

    //=======================================================FenGeXian=================================================

    /*
     商店展示可订阅的知识库(订阅知识库集合)
     */

    const canSubDatasets = await getSubscriptionDataSets({ userId });

    jsonRes<any[]>(res, {
      data: canSubDatasets
    });
  } catch (err) {
    jsonRes(res, {
      code: 500,
      error: err
    });
  }
}
