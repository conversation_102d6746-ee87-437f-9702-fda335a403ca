LOG_DEPTH=3
# 默认用户密码，用户名为 root，每次重启时会自动更新。
DEFAULT_ROOT_PSW=123456
# 数据库最大连接数
DB_MAX_LINK=50
# token
TOKEN_KEY=dfdasfdas
# 文件阅读时的秘钥
FILE_TOKEN_KEY=filetokenkey
# root key, 最高权限
ROOT_KEY=fdafasd
# openai 基本地址，可用作中转。
#OPENAI_BASE_URL=https://api.openai.com/v1
# oneapi 地址，可以使用 oneapi 来实现多模型接入 线上环境
ONEAPI_URL=http://122.14.231.165:3001/v1
# # 通用key。可以是 openai 的也可以是 oneapi 的。
# # 此处逻辑：优先走 ONEAPI_URL，如果填写了 ONEAPI_URL，key 也需要是 ONEAPI 的 key
# CHAT_API_KEY=sk-KiB95G0965jZw2P2AbCa36Ce2c94466d8b69E2Be47E1A725

# 测试环境
# ONEAPI_URL=http://122.14.231.166:3001/v1
CHAT_API_KEY=sk-KiB95G0965jZw2P2AbCa36Ce2c94466d8b69E2Be47E1A725

# mongo 数据库连接参数
# 本地测试测试库
MONGODB_URI=*************************************************************************
# 本地测试连接生产库
# MONGODB_URI=****************************************************************************
# 生产机器
# MONGODB_URI=******************************************************************************************
# PG 数据库连接参数
PG_URL=**************************************************/postgres
# 商业版地址
PRO_URL=
# 首页路径
HOME_URL=/
# Loki Log Path
# LOKI_LOG_URL=

MINIO_ACCESS_KEY=g3rYV34s5JWzNlZel5
MINIO_SECRET_KEY=kbnTBIPvCDmOvwlD32yyVwTxWamuG55jDhHX09E
MINIO_URL=minio://admin:1qaz2wsx@**************:9002?useSSL=false
# PPTX预览地址
PPTX_URL=http://*************:8012/demo/

