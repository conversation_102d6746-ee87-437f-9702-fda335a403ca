import type { ChatItemType } from '@wiserag/global/core/chat/type.d';
import type { ChatMessageItemType } from '@wiserag/global/core/ai/type.d';
import type { ModuleItemType, FlowModuleItemType } from '@wiserag/global/core/module/type.d';
import type { Edge, Node } from 'reactflow';
import { customAlphabet } from 'nanoid';
import { moduleTemplatesFlat } from '@/web/core/modules/template/system';
import { adaptRole_Message2Chat } from '@wiserag/global/core/chat/adapt';
import { EDGE_TYPE } from '@wiserag/global/core/module/node/constant';
import { UserInputModule } from '@wiserag/global/core/module/template/system/userInput';
import { ChatCompletionRequestMessageRoleEnum } from '@wiserag/global/core/ai/constant';
const nanoid = customAlphabet('abcdefghijklmnopqrstuvwxyz1234567890', 6);

/**
 * 函数“gptMessage2ChatType”通过映射和调整属性，将“ChatMessageItemType”对象数组转换为“ChatItemType”对象数组。
 *
 * @param messages
 * “gptMessage2ChatType”函数采用“ChatMessageItemType”对象数组作为输入，该数组由函数签名中的“messages”参数表示。每个“ChatMessageItemType”对象通常包含有关聊天中消息的信息，例如“dataId”
 * @return
 * 函数“gptMessage2ChatType”将“ChatMessageItemType”对象数组作为输入，并返回“ChatItemType”对象数组。每个“ChatItemType”对象包含来自输入对象的“dataId”属性、对输入对象的“role”属性调用“adaptRole_Message2Chat”函数的结果以及“content”属性
 */
export const gptMessage2ChatType = (messages: ChatMessageItemType[]): ChatItemType[] => {
  return messages.map((item) => ({
    dataId: item.dataId,
    obj: adaptRole_Message2Chat(item.role),
    value: item.content || ''
  }));
};
/**
 * 函数“textAdaptGptResponse”接受诸如 text、model、finish_reason 和 extraData 等参数，为 GPT 模型创建 JSON 字符串响应。
 *
 * @param  `textAdaptGptResponse` 函数接受一个对象作为其参数，该对象具有以下属性：
 * @return 函数“textAdaptGptResponse”返回一个 JSON
 * 字符串，表示具有提供的参数和默认值的对象。该对象包括“id”、“object”、“created”、“model”和“choices”等属性，其中“text”用于确定“choices”数组中的“delta”属性。
 */

export const textAdaptGptResponse = ({
  text,
  model = '',
  finish_reason = null,
  extraData = {}
}: {
  model?: string;
  text: string | null;
  finish_reason?: null | 'stop';
  extraData?: Object;
}) => {
  return JSON.stringify({
    ...extraData,
    id: '',
    object: '',
    created: 0,
    model,
    choices: [
      {
        delta:
          text === null
            ? {}
            : { role: ChatCompletionRequestMessageRoleEnum.Assistant, content: text },
        index: 0,
        finish_reason
      }
    ]
  });
};
/**
 * 函数“appModule2FlowNode”通过合并模板和项目数据来为流程模块项目创建节点对象。
 *
 * @param
 * 此代码片段定义了一个函数“appModule2FlowNode”，该函数将一个对象作为参数，其属性“item”类型为“ModuleItemType”。然后该函数处理这个“item”以创建一个“FlowModuleItemType”类型的新对象，并返回一个“Node”对象
 * @return 函数“appModule2FlowNode”返回一个具有以下属性的对象：
 * - `id`：输入项的模块 ID
 * - `type`：输入项的流类型
 * - `data`：基于模板的输入项的修改版本，具有更新的输入、输出和头像
 * - `position`：输入项的位置或默认位置
 */

export const appModule2FlowNode = ({
  item
}: {
  item: ModuleItemType;
}): Node<FlowModuleItemType> => {
  // init some static data
  const template =
    moduleTemplatesFlat.find((template) => template.flowType === item.flowType) || UserInputModule;

  const concatInputs = template.inputs.concat(
    item.inputs.filter((input) => !template.inputs.find((item) => item.key === input.key))
  );
  const concatOutputs = item.outputs.concat(
    template.outputs.filter(
      (templateOutput) => !item.outputs.find((item) => item.key === templateOutput.key)
    )
  );

  // replace item data
  const moduleItem: FlowModuleItemType = {
    ...template,
    ...item,
    avatar: template?.avatar || item.avatar,
    inputs: concatInputs.map((templateInput) => {
      // use latest inputs
      const itemInput = item.inputs.find((item) => item.key === templateInput.key) || templateInput;
      return {
        ...templateInput,
        value: itemInput.value
      };
    }),
    outputs: concatOutputs.map((output) => {
      // unChange outputs
      const templateOutput = template.outputs.find((item) => item.key === output.key);

      return {
        ...(templateOutput ? templateOutput : output),
        targets: output.targets || []
      };
    })
  };

  return {
    id: item.moduleId,
    type: item.flowType,
    data: moduleItem,
    position: item.position || { x: 0, y: 0 }
  };
};
/**
 * 函数“appModule2FlowEdge”根据模块的输出和目标在模块之间创建边缘。
 *
 * @param
 * “appModule2FlowEdge”函数采用一个对象作为参数，其属性“modules”是一个“ModuleItemType”对象的数组。该函数迭代“modules”数组中的每个模块，然后对于该模块的每个输出，它迭代目标
 * @return `appModule2FlowEdge` 函数返回一个 `Edge`
 * 对象数组。每个“Edge”对象表示流中模块之间的连接，具有“source”、“target”、“sourceHandle”、“targetHandle”、“id”和“type”等属性。
 */

export const appModule2FlowEdge = ({ modules }: { modules: ModuleItemType[] }) => {
  const edges: Edge[] = [];
  modules.forEach((module) =>
    module.outputs.forEach((output) =>
      output.targets?.forEach((target) => {
        edges.push({
          source: module.moduleId,
          target: target.moduleId,
          sourceHandle: output.key,
          targetHandle: target.key,
          id: nanoid(),
          type: EDGE_TYPE
        });
      })
    )
  );

  return edges;
};
