import { POST } from '@wiserag/service/common/api/plusRequest';
import type {
  AuthOutLinkChatProps,
  AuthOutLinkLimitProps,
  AuthOutLinkInitProps,
  AuthOutLinkResponse
} from '@wiserag/global/support/outLink/api.d';
import { authOutLinkValid } from '@wiserag/service/support/permission/auth/outLink';
import { getUserAndAuthBalance } from '@wiserag/service/support/user/controller';
import { AuthUserTypeEnum } from '@wiserag/global/support/permission/constant';
import { OutLinkErrEnum } from '@wiserag/global/common/error/code/outLink';
import { OutLinkSchema } from '@wiserag/global/support/outLink/type';

/**
 * 函数“authOutLinkInit”初始化外部链接的身份验证过程。
 *
 * @param data “authOutLinkInit”函数中的“data”参数的类型为“AuthOutLinkInitProps”。它包含初始化出站链路身份验证过程所需的信息。
 * @return 如果全局“feConfigs”对象没有属性“isPlus”，则返回一个
 * Promise，其中包含“data”参数中的“outLinkUid”对象。否则，将使用“data”参数向“/support/outLink/authInit”发出 POST
 * 请求，并且响应是类型为“AuthOutLinkResponse”的 Promise。
 */
export function authOutLinkInit(data: AuthOutLinkInitProps): Promise<AuthOutLinkResponse> {
  if (!global.feConfigs?.isPlus) return Promise.resolve({ uid: data.outLinkUid });
  return POST<AuthOutLinkResponse>('/support/outLink/authInit', data);
}
/**
 * 函数“authOutLinkChatLimit”检查用户是否是 Plus 会员，然后返回 outLinkUid 或发出 POST 请求来验证聊天开始。
 *
 * @param data “authOutLinkChatLimit”函数中的“data”参数的类型为“AuthOutLinkLimitProps”。它用于传递验证传出链接聊天限制所需的信息。
 * @return 如果全局“feConfigs”对象没有属性“isPlus”，则返回一个
 * Promise，其中包含“data”参数中的“outLinkUid”对象。否则，将使用“data”参数向“/support/outLink/authChatStart”发出 POST
 * 请求，并且响应是“AuthOutLinkResponse”类型的 Promise。
 */

export function authOutLinkChatLimit(data: AuthOutLinkLimitProps): Promise<AuthOutLinkResponse> {
  if (!global.feConfigs?.isPlus) return Promise.resolve({ uid: data.outLinkUid });
  return POST<AuthOutLinkResponse>('/support/outLink/authChatStart', data);
}
/**
 * TypeScript 中的函数“authOutLink”是一个异步函数，用于处理出站链接的身份验证并返回带有特定数据的 Promise。
 *
 * @param  “authOutLink”函数是一个异步函数，它接收具有可选属性“shareId”和“outLinkUid”的对象。它返回一个 Promise，该 Promise
 * 解析为包含“uid”、“appId”和“shareChat”的对象。
 * @return
 * 函数“authOutLink”返回一个具有属性“uid”、“appId”和“shareChat”的对象，其中“uid”是一个字符串，“appId”是一个字符串，“shareChat”是一个类型为“的对象”
 * OutLinkSchema`。
 */

export const authOutLink = async ({
  shareId,
  outLinkUid
}: {
  shareId?: string;
  outLinkUid?: string;
}): Promise<{
  uid: string;
  appId: string;
  shareChat: OutLinkSchema;
}> => {
  if (!outLinkUid) {
    return Promise.reject(OutLinkErrEnum.linkUnInvalid);
  }
  //检查 shareId 是否有效
  const result = await authOutLinkValid({ shareId });

  const { uid } = await authOutLinkInit({
    outLinkUid,
    tokenUrl: result.shareChat.limit?.hookUrl
  });

  return {
    ...result,
    uid
  };
};
/**
 * 函数“authOutLinkChatStart”处理通过外部链接启动聊天会话的身份验证，在返回必要的详细信息之前检查余额和聊天限制。
 *
 * @param  `authOutLinkChatStart` 函数接受以下参数：
 * @return 函数“authOutLinkChatStart”返回一个具有以下属性的对象：
 * - `authType` 设置为 `AuthUserTypeEnum.token`
 * - 来自“shareChat”对象的“responseDetail”
 * - `user` 对象包含用户信息和余额
 * - 来自“shareChat”对象的“appId”
 * - 来自经过身份验证的用户信息的“uid”
 */

export async function authOutLinkChatStart({
  shareId,
  ip,
  outLinkUid,
  question
}: AuthOutLinkChatProps & {
  shareId: string;
}) {
  // get outLink and app
  const { shareChat, appId } = await authOutLinkValid({ shareId });

  // check balance and chat limit
  const [user, { uid }] = await Promise.all([
    getUserAndAuthBalance({ tmbId: shareChat.tmbId, minBalance: 0 }),
    authOutLinkChatLimit({ outLink: shareChat, ip, outLinkUid, question })
  ]);

  return {
    authType: AuthUserTypeEnum.token,
    responseDetail: shareChat.responseDetail,
    user,
    appId,
    uid
  };
}
