import { GET, POST, PUT } from '@/web/common/api/request';
import { hashStr } from '@wiserag/global/common/string/tools';
import type { ResLogin } from '@/global/support/api/userRes.d';
import { UserAuthTypeEnum } from '@wiserag/global/support/user/constant';
import { UserUpdateParams } from '@/types/user';
import { UserType } from '@wiserag/global/support/user/type.d';
import type {
  FastLoginProps,
  OauthLoginProps,
  PostLoginProps
} from '@wiserag/global/support/user/api.d';
import { TeamMemberRoleEnum } from '@wiserag/global/support/user/team/constant';
import { GetAppChatLogsParams } from '@/global/core/api/appReq';

export interface ReadConfigResponse {
  data: {
    llmModels: any[]; // 根据实际数据结构调整类型
  };
}
export const readConfig = () => GET('/support/user/config/readConfig');
export const writeConfig = ({ llmModels }: { llmModels: any }) =>
  POST('/support/user/config/writeConfig', {
    llmModels: llmModels
  });

export const sendAuthCode = (data: {
  username: string;
  type: `${UserAuthTypeEnum}`;
  googleToken: string;
}) => POST(`/plusApi/support/user/inform/sendAuthCode`, data);

export const getTokenLogin = () =>
  GET<UserType>('/support/user/account/tokenLogin', {}, { maxQuantity: 1 });
export const oauthLogin = (params: OauthLoginProps) =>
  POST<ResLogin>('/plusApi/support/user/account/login/oauth', params);
export const postFastLogin = (params: FastLoginProps) =>
  POST<ResLogin>('/plusApi/support/user/account/login/fastLogin', params);

/**
 * `postRegister` 函数使用用户名、密码、代码和可选的 informerId 注册用户。
 *
 * @param  `postRegister` 函数用于使用提供的用户名、密码、代码和可选的 informerId 注册用户。密码在请求中发送之前会经过哈希处理。
 */
export const postRegister = ({
  username,
  password,
  code,
  inviterId
}: {
  username: string;
  code: string;
  password: string;
  inviterId?: string;
}) =>
  POST<ResLogin>(`/plusApi/support/user/account/register/emailAndPhone`, {
    username,
    code,
    inviterId,
    password: hashStr(password)
  });

/**
 * 函数“postFindPassword”使用验证码和哈希密码更新用户的密码。
 *
 * @param  `postFindPassword` 函数接受一个对象作为其参数，具有三个属性：
 */
export const postFindPassword = ({
  username,
  code,
  password
}: {
  username: string;
  code: string;
  password: string;
}) =>
  POST<ResLogin>(`/plusApi/support/user/account/password/updateByCode`, {
    username,
    code,
    password: hashStr(password)
  });

/**
 * 该功能通过验证旧密码并设置新密码来更新用户的密码。
 *
 * @param  updatePasswordByOld
 * 函数接受一个对象作为参数，该对象具有两个属性：“oldPsw”和“newPsw”，均为字符串类型。然后，该函数使用“old”的哈希值向端点“/support/user/account/updatePasswordByOld”发出
 * POST 请求
 */
export const updatePasswordByOld = ({ oldPsw, newPsw }: { oldPsw: string; newPsw: string }) =>
  POST('/support/user/account/updatePasswordByOld', {
    oldPsw: hashStr(oldPsw),
    newPsw: hashStr(newPsw)
  });
//授权码状态查询
export const getAllauthorizationCode = ({
  pageNum,
  pageSize
}: {
  pageNum: number;
  pageSize: number;
}) =>
  POST('/support/user/authorizationCodeManage/authorizationCodeManageTable', {
    pageNum: pageNum,
    pageSize: pageSize
  });
//授权码生成
export const authorizationCodeAdd = () =>
  GET('/support/user/authorizationCodeManage/authorizationCodeManageAdd');
// 查询全部团队
export const getAllTeam = () => GET('/support/user/account/getAllTeam');
// 用户管理表格
export const userManageTable = ({
  teamId,
  userName,
  pageNum,
  pageSize
}: {
  teamId: string;
  userName: string;
  pageNum: number;
  pageSize: number;
}) =>
  POST('/support/user/account/userManageTable', {
    teamId: teamId,
    userName: userName,
    pageNum: pageNum,
    pageSize: pageSize
  });
// 用户新增表格
export const userManageAdd = ({ name, phone }: { name: string; phone: string }) =>
  POST('/support/user/account/userManageAdd', {
    name: name,
    phone: phone
  });
// 用户编辑
export const userManageEdit = ({
  user,
  visualName,
  validityPeriod
}: {
  user: any;
  visualName: string;
  validityPeriod: any;
}) =>
  POST('/support/user/account/userManageEdit', {
    id: user,
    visualName: visualName,
    validityPeriod: validityPeriod
  });
// 用户删除
export const userManageDelete = ({ deleteId }: { deleteId: any }) =>
  GET('/support/user/account/userManageDelete', { deleteId });

// prompt管理表格
export const promptTable = ({
  keyword,
  pageNum,
  pageSize
}: {
  keyword: string;
  pageNum: number;
  pageSize: number;
}) =>
  POST('/support/user/prompt/promptTable', {
    keyword: keyword,
    pageNum: pageNum,
    pageSize: pageSize
  });
// prompt管理新增
export const promptAdd = ({
  title,
  desc,
  aiChatQuoteTemplate,
  aiChatQuotePrompt
}: {
  title: string;
  desc: string;
  aiChatQuoteTemplate: string;
  aiChatQuotePrompt: string;
}) =>
  POST('/support/user/prompt/promptAdd', {
    title: title,
    desc: desc,
    aiChatQuoteTemplate: aiChatQuoteTemplate,
    aiChatQuotePrompt: aiChatQuotePrompt
  });
// prompt管理编辑
export const promptEdit = ({
  id,
  title1,
  desc1,
  aiChatQuoteTemplate1,
  aiChatQuotePrompt1
}: {
  id: string;
  title1: string;
  desc1: string;
  aiChatQuoteTemplate1: string;
  aiChatQuotePrompt1: string;
}) =>
  POST('/support/user/prompt/promptEdit', {
    id: id,
    title: title1,
    desc: desc1,
    aiChatQuoteTemplate: aiChatQuoteTemplate1,
    aiChatQuotePrompt: aiChatQuotePrompt1
  });
// prompt管理删除
export const promptDelete = ({ deleteId }: { deleteId: any }) =>
  GET('/support/user/prompt/promptDelete', { deleteId });

//获取当前用户为管理员(创建者)的团队列表信息
export const teamInfo = () => GET('/support/user/team/teamInfo');
//查询团队列表
export const teamManageTable = ({
  teamId,
  pageNum,
  pageSize
}: {
  teamId: any;
  pageNum: number;
  pageSize: number;
}) =>
  POST('/support/user/team/teamManageTable', {
    teamIdparams: teamId,
    pageNum: pageNum,
    pageSize: pageSize
  });
// 团队邀请成员
export const createTeamMember = ({
  teamId,
  userName,
  role
}: {
  teamId: any;
  userName: string;
  role: any;
}) =>
  POST('/support/user/team/createTeamMember', {
    teamId: teamId,
    userName: userName,
    role: role
  });
// 团队用户删除
export const deleteTeamMember = ({ deleteId }: { deleteId: any }) =>
  GET('/support/user/team/deleteTeamMember', { deleteId });
// 团队用户修改role
export const editTeamMemberRole = ({ editId, editRole }: { editId: any; editRole: string }) =>
  POST('/support/user/team/editTeamMemberRole', {
    id: editId,
    role: editRole
  });
// 修改团队名称
export const editTeamName = ({ teamId, teamName }: { teamId: any; teamName: any }) =>
  POST('/support/user/team/editTeamName', {
    teamId: teamId,
    teamName: teamName
  });
// 工单处理表格
// export const workOrderTable = ({ page }: { page: number; }) =>
//   POST('/support/user/workorder/workOrderTable', {
//     page: page
//   });
export const workOrderTable = (data: GetAppChatLogsParams) =>
  POST(`/support/user/workorder/workOrderTable`, data);
// export const workOrderTable = ({
//     pageNum,
//     pageSize
//   }: {
//     pageNum: number;
//     pageSize: number;
//   }) =>
//     POST('/support/user/workorder/workOrderTable', {
//       pageNum: pageNum,
//       pageSize: pageSize
//     });
/**
 * `postLogin` 函数在对密码进行哈希处理后发送 POST 请求以通过密码登录用户。
 *
 * @param  `postLogin` 函数用于使用提供的数据向“/support/user/account/loginByPassword”端点发出 POST
 * 请求。该函数接受一个对象作为参数，其属性包括“password”和其他未指定的属性（“...props”）。 `password` 属性使用以下方法进行哈希处理
 */
export const postLogin = ({ password, ...props }: PostLoginProps) =>
  POST<ResLogin>('/support/user/account/loginByPassword', {
    ...props,
    password: hashStr(password)
  });
export const entryCode = ({ code }: { code: any }) =>
  POST('/support/user/account/entryCode', { code });
export const cookieUserMessage = ({ token }: { token: any }) =>
  GET('/support/user/4A/4AuserMessage', { token });
/**
 * “loginOut”函数发送 GET 请求以从其帐户中注销用户。
/**
 * 函数“putUserInfo”发送 PUT 请求以更新用户帐户信息。
 * 
 * @param data UserUpdateParams 是一个接口或类型，可能包含更新用户信息所需的参数。它可以包括姓名、电子邮件、密码或任何其他可以更新的用户相关数据等字段。
 */

export const loginOut = () => GET('/support/user/account/loginout');

export const putUserInfo = (data: UserUpdateParams) => PUT('/support/user/account/update', data);
