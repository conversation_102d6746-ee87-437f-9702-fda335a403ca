import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { MongoUser } from '@wiserag/service/support/user/schema';
import { createJWT, setCookie } from '@wiserag/service/support/permission/controller';
import { connectToDatabase } from '@/service/mongo';
import { getUserDetail } from '@wiserag/service/support/user/controller';
import type { PostLoginProps } from '@wiserag/global/support/user/api.d';
import { UserStatusEnum } from '@wiserag/global/support/user/constant';
import { MongoEntryCode } from '@wiserag/service/support/user/entryCode';
import * as crypto from 'crypto';

interface UserModelSchema {
  _id: string;
  code?: string; // 添加 code 字段
  // 其他字段...
}
// 将公钥字符串转换为正确的格式
const PUBLIC_KEY: string = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAouGyzprMNye1b2gyEIB5
OlSz8volac8190JjB8LyWl4naAlQhm7uJxr1SUS9AVihK5XUcz5mOyWsa48fhG+L5
RBSq31nvr2L9yOV8vNTWMFsPfSRELDRlmAJHrRkLsX6Ie+2OQTX4FFJ94x10kyLyGfN/
c66JeDngVmDzNu1rFg+98KtkLNsZixqqt/ZWhfhikAYKn7Skihdp95nfGmKNstk3yv5K
s5zwhNSF1cZhoBrMJ1tek2gY/the+JtwKPxFxLG43dnOtsdWrSqKNdBpcJFy1nh2Rdp7
R1uRyfKMnt0yk/2OL5XatDHljX3VXQoA4w7Ta6S42Fx66kmT+E1twIDAQAB
-----END PUBLIC KEY-----`;

/**
 * Base64 解码
 * @param encodedInput Base64编码的字符串
 * @returns 解码后的字符串
 */
export function decode(encodedInput: string): string {
  console.log('开始解码:', encodedInput);
  const decodedBytes: Buffer = Buffer.from(encodedInput, 'base64');
  const result: string = decodedBytes.toString('utf8');
  console.log('解码结果:', result);
  return result;
}

/**
 * 验证 RSA 签名
 * @param signatureBase64 包含签名和参数的Base64字符串，格式为"签名##参数"
 * @returns 验证成功返回参数，失败返回null
 * @throws 如果验证过程中出错
 */
export async function verify(signatureBase64: string): Promise<string | null> {
  try {
    console.log('开始验证签名:', signatureBase64);
    const eqs: string[] = signatureBase64.split('##');
    console.log('分割结果:', eqs);

    if (eqs.length !== 2) {
      throw new Error('Invalid signature format');
    }

    const param: string = decode(eqs[1]);
    const verify: crypto.Verify = crypto.createVerify('RSA-SHA256');
    verify.update(param, 'utf8');

    const signatureBytes: Buffer = Buffer.from(eqs[0], 'base64');
    console.log('签名长度:', signatureBytes.length);
    console.log('公钥格式:', PUBLIC_KEY);

    const isValid: boolean = verify.verify(PUBLIC_KEY, signatureBytes);
    console.log('验证结果:', isValid);

    return isValid ? param : null;
  } catch (error: unknown) {
    console.error('验证失败:', error);
    if (error instanceof Error) {
      throw new Error(`Verification failed: ${error.message}`);
    }
    throw new Error('Verification failed with unknown error');
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    await connectToDatabase();

    const entryCode = await MongoEntryCode.find({});
    console.log('🚀 ~ handler ~ entryCode:', entryCode);

    // 确保 entryCode 存在
    if (entryCode.length === 0) {
      return jsonRes(res, {
        code: 400,
        error: '未找到校验码'
      });
    }

    const result = await verify((entryCode[0] as UserModelSchema).code as string);
    console.log('最终结果:', result);

    // 检查 result 是否为 null
    if (result !== null) {
      const { username, password } = req.body as PostLoginProps;
      if (!username || !password) {
        throw new Error('缺少参数');
      }

      // 检测用户是否存在
      const authCert = await MongoUser.findOne(
        {
          username
        },
        'status createTime expirationDate'
      );
      if (!authCert) {
        throw new Error('用户未注册');
      }

      if (authCert.status === UserStatusEnum.forbidden) {
        throw new Error('账号已停用，无法登录');
      }

      const user = await MongoUser.findOne({
        username,
        password
      });

      if (!user) {
        throw new Error('密码错误');
      }

      // 检查是否为 root 用户
      const isRootUser =
        user.username === 'root' && user._id.toString() === '667390ad5ed08143688783a2';

      let warningMessage = '';
      if (!isRootUser) {
        const currentDate = new Date(user.createTime);
        const expirationDate = new Date(user.expirationDate);
        const daysUntilExpiration = Math.ceil(
          (expirationDate.getTime() - currentDate.getTime()) / (1000 * 3600 * 24)
        );

        if (daysUntilExpiration < 0) {
          throw new Error('账户已过期，请联系管理员');
        } else if (daysUntilExpiration <= 30) {
          warningMessage = `您的账户将在 ${daysUntilExpiration} 天后过期，请及时延长。`;
        }
      }

      const userDetail = await getUserDetail({
        tmbId: user?.lastLoginTmbId,
        userId: user._id
      });

      MongoUser.findByIdAndUpdate(user._id, {
        lastLoginTmbId: userDetail.team.tmbId
      });

      const token = createJWT(userDetail);
      setCookie(res, token);

      jsonRes(res, {
        data: {
          user: {
            ...userDetail,
            warning: warningMessage,
            code: (entryCode[0] as UserModelSchema).code
          },
          token
        }
      });
    } else {
      return jsonRes(res, {
        code: 400,
        error: '校验码验证失败，无法登录'
      });
    }
  } catch (err) {
    jsonRes(res, {
      code: 500,
      error: err
    });
  }
}
