{"systemEnv": {"openapiPrefix": "wisegpt", "vectorMaxProcess": 15, "qaMaxProcess": 15, "pgHNSWEfSearch": 100}, "qaModels": [{"model": "Qwen1.5-14B-<PERSON><PERSON>", "name": "Qwen1.5-14B-<PERSON><PERSON>", "price": 0, "maxContext": 4000, "maxResponse": 4000, "quoteMaxToken": 2000, "maxTemperature": 1.2, "censor": false, "vision": false, "defaultSystemChatPrompt": ""}], "chatModels": [{"model": "Qwen1.5-14B-<PERSON><PERSON>", "name": "Qwen1.5-14B-<PERSON><PERSON>", "price": 0, "maxContext": 4000, "maxResponse": 4000, "quoteMaxToken": 2000, "maxTemperature": 1.2, "censor": false, "vision": false, "defaultSystemChatPrompt": ""}], "llmModels": [{"model": "Qwen1.5-14B-<PERSON><PERSON>", "name": "Qwen1.5-32B<PERSON><PERSON>-<PERSON><PERSON>", "maxContext": 128000, "maxResponse": 4000, "quoteMaxToken": 4000, "maxTemperature": 1.2, "inputPrice": 0.008, "outputPrice": 0.008, "censor": false, "vision": false, "datasetProcess": true, "toolChoice": false, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "Qwen2-7B-Instruct", "name": "Qwen2-7B-Instruct", "maxContext": 128000, "maxResponse": 4000, "quoteMaxToken": 4000, "maxTemperature": 1.2, "inputPrice": 0.008, "outputPrice": 0.008, "censor": false, "vision": false, "datasetProcess": true, "toolChoice": false, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "ChatGLM3-6b", "name": "ChatGLM3-6b", "maxContext": 128000, "maxResponse": 4000, "quoteMaxToken": 4000, "maxTemperature": 1.2, "inputPrice": 0.008, "outputPrice": 0.008, "censor": false, "vision": false, "datasetProcess": true, "toolChoice": false, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {"frequency_penalty": 0.1}}, {"model": "glm-4-9b-chat", "name": "glm-4-9b-chat", "maxContext": 4000, "maxResponse": 4000, "quoteMaxToken": 4000, "maxTemperature": 1.2, "inputPrice": 0.008, "outputPrice": 0.008, "censor": false, "vision": false, "datasetProcess": true, "toolChoice": false, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {"stop_token_ids": [151329, 151336, 151338]}}, {"model": "llama3-Chinese-chat", "name": "llama3-Chinese-chat", "maxContext": 4096, "maxResponse": 4096, "quoteMaxToken": 13000, "maxTemperature": 1.2, "inputPrice": 0.02, "outputPrice": 0.02, "censor": false, "vision": false, "datasetProcess": true, "toolChoice": false, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "CodeQwen1.5-7B-<PERSON><PERSON>", "name": "千问1.5-7B-Code", "maxContext": 4096, "maxResponse": 4096, "quoteMaxToken": 13000, "maxTemperature": 1.2, "inputPrice": 0.02, "outputPrice": 0.02, "censor": false, "vision": false, "datasetProcess": true, "toolChoice": false, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "Qwen1.5-14B-<PERSON><PERSON>", "name": "千问1.5-14B-chat", "maxContext": 4096, "maxResponse": 4096, "quoteMaxToken": 13000, "maxTemperature": 1.2, "inputPrice": 0.12, "outputPrice": 0.12, "censor": false, "vision": false, "datasetProcess": true, "toolChoice": false, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "telechat", "name": "中国电信大语言模型", "maxContext": 4096, "maxResponse": 4096, "quoteMaxToken": 13000, "maxTemperature": 1.2, "inputPrice": 0.08, "outputPrice": 0.08, "censor": false, "vision": false, "datasetProcess": true, "toolChoice": false, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "qwen-max", "name": "千问Max", "maxContext": 6000, "maxResponse": 6000, "quoteMaxToken": 13000, "maxTemperature": 1.2, "inputPrice": 0.12, "outputPrice": 0.12, "censor": false, "vision": false, "datasetProcess": true, "toolChoice": false, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "qwen-turbo", "name": "千问Turbo", "maxContext": 6000, "maxResponse": 6000, "quoteMaxToken": 13000, "maxTemperature": 1.2, "inputPrice": 0.008, "outputPrice": 0.008, "censor": false, "vision": false, "datasetProcess": true, "toolChoice": false, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "Qwen1.5-14B-<PERSON><PERSON>", "name": "Qwen1.5-14B-<PERSON><PERSON>", "maxContext": 16384, "maxResponse": 4096, "price": 0, "quoteMaxToken": 16384, "maxTemperature": 1.2, "inputPrice": 0, "outputPrice": 0, "censor": false, "vision": false, "datasetProcess": true, "toolChoice": false, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "qwen-max", "name": "qwen-max-test", "maxContext": 4096, "maxResponse": 4096, "quoteMaxToken": 13000, "maxTemperature": 1.2, "inputPrice": 0.02, "outputPrice": 0.02}], "vectorModels": [{"model": "bge-large-zh-v1.5", "name": "bge-embedding", "price": 0, "defaultToken": 700, "maxToken": 4000, "weight": 100}, {"model": "m3e", "name": "wise-embedding", "price": 0, "defaultToken": 700, "maxToken": 3000, "weight": 100}, {"model": "bge-m3", "name": "bge-m3", "price": 0, "defaultToken": 700, "maxToken": 3000, "weight": 100}], "reRankModels": [{"model": "bge-rerank-base-fintune-PAI", "name": "BGE-BASE-检索重排(直接服务部署PAI-GPU,适配4.6.8二开版本,微调后)", "charsPointsPrice": 0, "requestUrl": "http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/reranker_base_finetune/api/v1/rerank", "requestAuth": "OTEzZWQ2OTc4YTBlZWY2MDBjYjc5ZGNjMDViYzg1NmIyNThkODg5MQ=="}, {"model": "bge-rerank-base-fintune", "name": "BGE-BASE-检索重排(直接服务部署GPU、非OneApi部署,适配4.6.8二开版本,微调后)", "charsPointsPrice": 0, "requestUrl": "http://218.77.35.16:6006/api/v1/rerank", "requestAuth": "mytoken"}, {"model": "bge-rerank-v2-m3-GPU-17", "name": "BGE-V2M3-检索重排(直接服务部署GPU、非OneApi部署,适配4.6.8二开版本)", "charsPointsPrice": 0, "requestUrl": "http://49.7.224.17:6007/api/v1/rerank", "requestAuth": "mytoken"}, {"model": "bge-reranker-base-0.2-GPU-17", "name": "BGE-BASE-检索重排(直接服务部署GPU、非OneApi部署)", "charsPointsPrice": 0, "requestUrl": "http://49.7.224.17:6006/api/v1/rerank", "requestAuth": "mytoken"}], "audioSpeechModels": [{"model": "tts-1", "name": "OpenAI TTS1", "inputPrice": 0, "outputPrice": 0, "voices": [{"label": "<PERSON><PERSON>", "value": "alloy", "bufferId": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"label": "Echo", "value": "echo", "bufferId": "openai-Echo"}, {"label": "Fable", "value": "fable", "bufferId": "openai-Fable"}, {"label": "Onyx", "value": "onyx", "bufferId": "openai-Onyx"}, {"label": "Nova", "value": "nova", "bufferId": "openai-Nova"}, {"label": "Shimmer", "value": "shimmer", "bufferId": "openai-Shimmer"}]}], "whisperModel": {"model": "whisper-1", "name": "Whisper1", "inputPrice": 0, "outputPrice": 0}}