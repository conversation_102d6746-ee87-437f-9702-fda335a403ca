import { NextApiResponse } from 'next';
import { ModuleInputKeyEnum } from '@wiserag/global/core/module/constants';
import { ModuleOutputKeyEnum } from '@wiserag/global/core/module/constants';
import type { ChatDispatchProps, RunningModuleItemType } from '@wiserag/global/core/module/type.d';
import { ModuleDispatchProps } from '@wiserag/global/core/module/type.d';
import type { ChatHistoryItemResType } from '@wiserag/global/core/chat/type.d';
import { FlowNodeInputTypeEnum, FlowNodeTypeEnum } from '@wiserag/global/core/module/node/constant';
import { ModuleItemType } from '@wiserag/global/core/module/type';
import { replaceVariable } from '@wiserag/global/common/string/tools';
import { responseWrite } from '@wiserag/service/common/response';
import { sseResponseEventEnum } from '@wiserag/service/common/response/constant';
import { getSystemTime } from '@wiserag/global/common/time/timezone';
import { initRunningModuleType } from '../core/modules/constant';

import { dispatchHistory } from './init/history';
import { dispatchChatInput } from './init/userChatInput';
import { dispatchChatCompletion } from './chat/oneapi';
import { dispatchDatasetSearch } from './dataset/search';
import { dispatchDatasetConcat } from './dataset/concat';
import { dispatchAnswer } from './tools/answer';
import { dispatchClassifyQuestion } from './agent/classifyQuestion';
import { dispatchContentExtract } from './agent/extract';
import { dispatchHttpRequest } from './tools/http';
import { dispatchHttp468Request } from './tools/http468';
import { dispatchHttp468WiseWebRequest } from './tools/http468WiseWeb';
import { dispatchAppRequest } from './tools/runApp';
import { dispatchCFR } from './tools/cfr';
import { dispatchRunPlugin } from './plugin/run';
import { dispatchPluginInput } from './plugin/runInput';
import { dispatchPluginOutput } from './plugin/runOutput';
import { valueTypeFormat } from './utils';

/* 上面的 TypeScript 代码定义了一个 `callbackMap` 对象，它将不同的 `FlowNodeTypeEnum`
值映射到相应的回调函数。每个“FlowNodeTypeEnum”值都与一个特定函数相关联，当遇到该特定枚举值时将调用该函数。这样可以根据流中节点的类型轻松分派不同的操作。 */
const callbackMap: Record<`${FlowNodeTypeEnum}`, Function> = {
  [FlowNodeTypeEnum.historyNode]: dispatchHistory,
  [FlowNodeTypeEnum.questionInput]: dispatchChatInput,
  [FlowNodeTypeEnum.answerNode]: dispatchAnswer,
  [FlowNodeTypeEnum.chatNode]: dispatchChatCompletion,
  [FlowNodeTypeEnum.datasetSearchNode]: dispatchDatasetSearch,
  [FlowNodeTypeEnum.datasetConcatNode]: dispatchDatasetConcat,
  [FlowNodeTypeEnum.classifyQuestion]: dispatchClassifyQuestion,
  [FlowNodeTypeEnum.contentExtract]: dispatchContentExtract,
  [FlowNodeTypeEnum.httpRequest]: dispatchHttpRequest,
  [FlowNodeTypeEnum.httpRequest468]: dispatchHttp468Request,
  [FlowNodeTypeEnum.httpRequest468WiseWeb]: dispatchHttp468WiseWebRequest,
  [FlowNodeTypeEnum.runApp]: dispatchAppRequest,
  [FlowNodeTypeEnum.pluginModule]: dispatchRunPlugin,
  [FlowNodeTypeEnum.pluginInput]: dispatchPluginInput,
  [FlowNodeTypeEnum.pluginOutput]: dispatchPluginOutput,
  [FlowNodeTypeEnum.cfr]: dispatchCFR,

  // none
  [FlowNodeTypeEnum.userGuide]: () => Promise.resolve()
};

/* running */

/**
 * TypeScript 中的“dispatchModules”函数异步处理模块、处理输入和输出并生成聊天响应。
 *
 * @param  `dispatchModules` 函数接受以下参数：
 * @return {
 *   [ModuleOutputKeyEnum.answerText]：chatAnswerText，
 *   [ModuleOutputKeyEnum.responseData]：chatResponse
 * }
 */
export async function dispatchModules({
  res,
  modules,
  histories = [],
  startParams = {},
  variables = {},
  user,
  stream = false,
  detail = false,
  ...props
}: ChatDispatchProps & {
  modules: ModuleItemType[];
  startParams?: Record<string, any>;
}) {
  // set sse response headers
  if (stream) {
    res.setHeader('Content-Type', 'text/event-stream;charset=utf-8');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('X-Accel-Buffering', 'no');
    res.setHeader('Cache-Control', 'no-cache, no-transform');
  }

  variables = {
    ...getSystemVariable({ timezone: user.timezone }),
    ...variables
  };

  const runningModules = loadModules(modules, variables);

  // let storeData: Record<string, any> = {}; // after module used
  let chatResponse: ChatHistoryItemResType[] = []; // response request and save to database 回复及保存
  let chatAnswerText = ''; // AI answer 回答
  let runningTime = Date.now(); // 运行时间
  /**
   * `pushStore` 函数根据输入参数更新聊天响应数据和答案文本。
   *
   * @param  `pushStore` 函数有两个参数：
   * @param  `pushStore` 函数有两个参数：
   */

  function pushStore(
    { inputs = [] }: RunningModuleItemType,
    {
      answerText = '',
      responseData
    }: {
      answerText?: string;
      responseData?: ChatHistoryItemResType | ChatHistoryItemResType[];
    }
  ) {
    const time = Date.now();
    if (responseData) {
      if (Array.isArray(responseData)) {
        chatResponse = chatResponse.concat(responseData);
      } else {
        chatResponse.push({
          ...responseData,
          runningTime: +((time - runningTime) / 1000).toFixed(2)
        });
      }
    }
    runningTime = time;

    const isResponseAnswerText =
      inputs.find((item) => item.key === ModuleInputKeyEnum.aiChatIsResponseText)?.value ?? true;
    if (isResponseAnswerText) {
      chatAnswerText += answerText;
    }
  }

  /**
   * 函数“moduleInput”根据提供的数据更新正在运行的模块的输入值。
   *
   * @param module
   * “moduleInput”函数中的“module”参数的类型为“RunningModuleItemType”，它可能表示当前正在运行或活动的模块。它包含有关模块输入的信息，例如键和值。
   * @param data moduleInput 函数中的 data 参数是一个记录对象，其中包含表示输入键及其在 module 对象中需要更新的对应值的键值对。
   * @return 函数“moduleInput”不显式返回任何内容，因此它隐式返回“undefined”。
   */
  function moduleInput(module: RunningModuleItemType, data: Record<string, any> = {}) {
    const updateInputValue = (key: string, value: any) => {
      const index = module.inputs.findIndex((item: any) => item.key === key);
      if (index === -1) return;
      module.inputs[index].value = value;
    };
    Object.entries(data).map(([key, val]: any) => {
      updateInputValue(key, val);
    });

    return;
  }

  /**
   * 函数“moduleOutput”处理模块输出并使用输出值更新目标模块。
   *
   * @param module “moduleOutput”函数中的“module”参数表示正在运行的模块项类型。它可能包含有关当前正在运行或正在较大系统或应用程序中处理的特定模块的信息。
   * @param result moduleOutput 函数中的 result 参数是一个记录对象，存储当前运行模块的输出值。它用于更新模块输出项的输出值，并将其传递给目标模块进行进一步处理。
   * @return moduleOutput 函数返回一个 Promise，该 Promise 解析为使用过滤后的 nextRunModules 作为参数调用 checkModulesCanRun
   * 函数的结果。
   */
  function moduleOutput(
    module: RunningModuleItemType,
    result: Record<string, any> = {}
  ): Promise<any> {
    pushStore(module, result);

    const nextRunModules: RunningModuleItemType[] = [];

    // Assign the output value to the next module
    module.outputs.map((outputItem) => {
      if (result[outputItem.key] === undefined) return;
      /* update output value */
      outputItem.value = result[outputItem.key];

      /* update target */
      outputItem.targets.map((target: any) => {
        // find module
        const targetModule = runningModules.find((item) => item.moduleId === target.moduleId);
        if (!targetModule) return;

        // push to running queue
        nextRunModules.push(targetModule);

        // update input
        moduleInput(targetModule, { [target.key]: outputItem.value });
      });
    });

    // Ensure the uniqueness of running modules
    const set = new Set<string>();
    const filterModules = nextRunModules.filter((module) => {
      if (set.has(module.moduleId)) return false;
      set.add(module.moduleId);
      return true;
    });

    return checkModulesCanRun(filterModules);
  }

  /**
   * 函数“checkModulesCanRun”在运行之前检查是否为提供的数组中的每个模块定义了所有输入值。
   *
   * @param modules `checkModulesCanRun` 函数采用 `RunningModuleItemType`
   * 对象数组作为其参数。每个“RunningModuleItemType”对象代表一个可以运行的模块并包含一个输入数组。
   * @return `checkModulesCanRun` 函数返回一个解析为 Promise 数组的 Promise。每个内部 Promise 对应于 `modules`
   * 数组中的一个模块，该模块满足已定义所有输入的条件（即没有值为 `undefined` 的输入）。如果模块满足此条件，则该函数使用“moduleInput”函数从模块中删除“switch”输入
   */
  function checkModulesCanRun(modules: RunningModuleItemType[] = []) {
    return Promise.all(
      modules.map((module) => {
        if (!module.inputs.find((item: any) => item.value === undefined)) {
          // remove switch
          moduleInput(module, { [ModuleInputKeyEnum.switch]: undefined });
          return moduleRun(module);
        }
      })
    );
  }

  /**
   * 函数“moduleRun”异步运行具有指定输入和输出的模块，处理响应数据格式和用户聊天输入。
   *
   * @param module
   * “moduleRun”函数中的“module”参数表示“RunningModuleItemType”类型的对象。该对象包含有关需要运行的特定模块的信息。它包括“name”、“showStatus”、“inputs”、“outputs”和“flowType”等属性。这
   * @return moduleRun 函数返回一个 Promise，该 Promise
   * 解析为包含各种属性的对象，例如“[ModuleOutputKeyEnum.finish]”、“[ModuleOutputKeyEnum.userChatInput]”和“[ModuleOutputKeyEnum.responseData]”。这些属性的具体值是根据函数内的逻辑确定的，包括“dispatchRes”和“params”的处理。
   */
  async function moduleRun(module: RunningModuleItemType): Promise<any> {
    if (res.closed) return Promise.resolve();

    if (stream && detail && module.showStatus) {
      responseStatus({
        res,
        name: module.name,
        status: 'running'
      });
    }

    // get module running params
    // 获取 模型的运行参数
    const params: Record<string, any> = {};
    module.inputs.forEach((item) => {
      params[item.key] = valueTypeFormat(item.value, item.valueType);
    });

    const dispatchData: ModuleDispatchProps<Record<string, any>> = {
      ...props,
      res,
      variables,
      histories,
      user,
      stream,
      detail,
      outputs: module.outputs,
      inputs: module.inputs,
      params
    };

    // run module
    // 运行 模块
    const dispatchRes: Record<string, any> = await (async () => {
      if (callbackMap[module.flowType]) {
        return callbackMap[module.flowType](dispatchData);
      }
      return {};
    })();

    // format response data. Add modulename and moduletype
    const formatResponseData = (() => {
      if (!dispatchRes[ModuleOutputKeyEnum.responseData]) return undefined;
      if (Array.isArray(dispatchRes[ModuleOutputKeyEnum.responseData])) {
        return dispatchRes[ModuleOutputKeyEnum.responseData];
      }

      return {
        moduleName: module.name,
        moduleType: module.flowType,
        ...dispatchRes[ModuleOutputKeyEnum.responseData]
      };
    })();

    // Pass userChatInput
    //
    const hasUserChatInputTarget = !!module.outputs.find(
      (item) => item.key === ModuleOutputKeyEnum.userChatInput
    )?.targets?.length;

    return moduleOutput(module, {
      [ModuleOutputKeyEnum.finish]: true,
      [ModuleOutputKeyEnum.userChatInput]: hasUserChatInputTarget
        ? params[ModuleOutputKeyEnum.userChatInput]
        : undefined,
      ...dispatchRes,
      [ModuleOutputKeyEnum.responseData]: formatResponseData
    });
  }

  // start process width initInput
  // 开始流程
  // 初始化
  const initModules = runningModules.filter((item) => initRunningModuleType[item.flowType]);

  // runningModules.forEach((item) => {
  //   console.log(item);
  // });

  initModules.map((module) =>
    // 提供的数据更新正在运行的模块的输入值
    moduleInput(module, {
      ...startParams,
      history: [] // abandon history field. History module will get histories from other fields.
    })
  );
  // 检查运行添加是否具备
  await checkModulesCanRun(initModules);

  // focus try to run pluginOutput
  const pluginOutputModule = runningModules.find(
    (item) => item.flowType === FlowNodeTypeEnum.pluginOutput
  );
  if (pluginOutputModule) {
    await moduleRun(pluginOutputModule);
  }

  return {
    [ModuleOutputKeyEnum.answerText]: chatAnswerText,
    [ModuleOutputKeyEnum.responseData]: chatResponse
  };
}

/* init store modules to running modules */
/**
 * 函数“loadModules”根据某些条件过滤和映射模块数组，并返回正在运行的模块的新数组。
 *
 * @param modules `modules` 参数是具有以下结构的对象数组：
 * @param variables loadModules 函数中的variables 参数是一个记录对象，用于存储变量的键值对。这些变量用于在加载过程中替换模块输入中的值。
 * @return
 * 在过滤和映射输入“modules”数组后，“loadModules”函数返回“RunningModuleItemType”对象的数组。每个“RunningModuleItemType”对象都包含模块信息，例如
 * moduleId、名称、flowType、showStatus、输入和输出。该函数过滤掉“moduleId”等于“FlowNodeTypeEnum.userGuide”的模块，并处理每个模块的输入和输出
 * 函数签名中定义了loadModules函数，并带有两个参数：modules类型为ModuleItemType[]，variables类型为Record<string, any>。
 * 函数使用filter方法对modules数组进行过滤，删除具有moduleId等于FlowNodeTypeEnum.userGuide的项。使用includes方法检查moduleId是否存在于数组[FlowNodeTypeEnum.userGuide]中。
 * 返回过滤后的数组。
 * 使用map方法对过滤后的modules数组中的每个项进行转换，创建一个新的对象。在map的回调函数中，创建一个新对象，具有以下属性：
 * moduleId，赋值为module.moduleId的值。
 * name，赋值为module.name的值。
 * flowType，赋值为module.flowType的值。
 * showStatus，赋值为module.showStatus的值。
 * inputs，是经过过滤和转换的module.inputs数组。
 * inputs数组使用filter方法进行过滤，过滤条件为：
 * type属性等于FlowNodeInputTypeEnum.systemInput。
 * connected属性为真值。
 * value属性不等于undefined。
 * 过滤后的inputs数组通过map方法进行转换。转换的回调函数对每个item创建一个新对象，新对象的属性为：
 * key，赋值为item.key的值。
 * value，根据typeof item.value为'string'的条件，判断是否调用replaceVariable函数替换item.value，或直接使用item.value。
 * valueType，赋值为item.valueType的值。
 * 类似于inputs属性，outputs属性也以相似的方式定义。对module.outputs数组使用map方法，转换每个项为一个新对象，新对象的属性为：
 * key，赋值为item.key的值。
 * answer，判断item.key是否等于ModuleOutputKeyEnum.answerText的值。
 * value，未定义。
 * valueType，赋值为item.valueType的值。
 * targets，赋值为item.targets的值。
 * 使用sort方法对outputs数组进行
 */

function loadModules(
  modules: ModuleItemType[],
  variables: Record<string, any>
): RunningModuleItemType[] {
  return modules
    .filter((item) => {
      return ![FlowNodeTypeEnum.userGuide].includes(item.moduleId as any);
    })
    .map((module) => {
      return {
        moduleId: module.moduleId,
        name: module.name,
        flowType: module.flowType,
        showStatus: module.showStatus,
        inputs: module.inputs
          .filter(
            (item) =>
              item.type === FlowNodeInputTypeEnum.systemInput ||
              item.connected ||
              item.value !== undefined
          ) // filter unconnected target input
          .map((item) => {
            const replace = ['string'].includes(typeof item.value);

            return {
              key: item.key,
              // variables replace
              value: replace ? replaceVariable(item.value, variables) : item.value,
              valueType: item.valueType
            };
          }),
        outputs: module.outputs
          .map((item) => ({
            key: item.key,
            answer: item.key === ModuleOutputKeyEnum.answerText,
            value: undefined,
            valueType: item.valueType,
            targets: item.targets
          }))
          .sort((a, b) => {
            // finish output always at last
            if (a.key === ModuleOutputKeyEnum.finish) return 1;
            if (b.key === ModuleOutputKeyEnum.finish) return -1;
            return 0;
          })
      };
    });
}

/* sse response modules staus */
/**
 * 函数responseStatus 更新Next.js API 路由中给定名称的响应状态。
 *
 * @param  `responseStatus` 函数接受一个具有以下参数的对象：
 * @return 如果未提供“name”参数，该函数将提前返回并且不执行“responseWrite”函数。
 */

export function responseStatus({
  res,
  status,
  name
}: {
  res: NextApiResponse;
  status?: 'running' | 'finish';
  name?: string;
}) {
  if (!name) return;
  responseWrite({
    res,
    event: sseResponseEventEnum.moduleStatus,
    data: JSON.stringify({
      status: 'running',
      name
    })
  });
}

/* get system variable */
/**
 * 函数“getSystemVariable”根据提供的时区返回当前系统时间。
 *
 * @param  `getSystemVariable` 函数将一个对象作为参数，该对象具有字符串类型的 `timezone`
 * 属性。然后，它使用提供的“timezone”值调用“getSystemTime”函数来获取该时区的当前时间，并返回一个设置了“cTime”属性的对象
 * @return
 * 函数“getSystemVariable”返回一个具有属性“cTime”的对象，其值是调用函数“getSystemTime”并将“timezone”参数传递给“getSystemVariable”的结果。
 */
export function getSystemVariable({ timezone }: { timezone: string }) {
  return {
    cTime: getSystemTime(timezone)
  };
}
