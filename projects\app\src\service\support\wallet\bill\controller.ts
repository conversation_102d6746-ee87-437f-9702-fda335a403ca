import { ConcatBillProps, CreateBillProps } from '@wiserag/global/support/wallet/bill/api';
import { addLog } from '@wiserag/service/common/system/log';
import { POST } from '@wiserag/service/common/api/plusRequest';
import { WiseGPTProUrl } from '@wiserag/service/common/system/constants';

export function createBill(data: CreateBillProps) {
  if (!WiseGPTProUrl) return;
  if (data.total === 0) {
    addLog.info('0 Bill', data);
  }
  try {
    POST('/support/wallet/bill/createBill', data);
  } catch (error) {}
}
export function concatBill(data: ConcatBillProps) {
  if (!WiseGPTProUrl) return;
  if (data.total === 0) {
    addLog.info('0 Bill', data);
  }
  try {
    POST('/support/wallet/bill/concatBill', data);
  } catch (error) {}
}
