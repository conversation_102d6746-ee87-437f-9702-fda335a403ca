import { AppSimpleEditConfigTemplateType } from '@wiserag/global/core/app/type';
import { GET } from '@wiserag/service/common/api/plusRequest';
import { WiseGPTProUrl } from '@wiserag/service/common/system/constants';

export async function getSimpleTemplatesFromPlus(): Promise<AppSimpleEditConfigTemplateType[]> {
  try {
    if (!WiseGPTProUrl) return [];

    return GET<AppSimpleEditConfigTemplateType[]>('/core/app/getSimpleTemplates');
  } catch (error) {
    return [];
  }
}
