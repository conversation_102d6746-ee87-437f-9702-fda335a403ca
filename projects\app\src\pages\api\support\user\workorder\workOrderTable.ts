import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { connectToDatabase } from '@/service/mongo';
import { MongoChatItem } from '@wiserag/service/core/chat/chatItemSchema';
import type { PagingData } from '@/types';
import { Types } from '@wiserag/service/common/mongo';
import type { GetAppChatLogsParams } from '@/global/core/api/appReq.d';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    await connectToDatabase();
    const { pageNum = 1, pageSize = 20 } = req.body as GetAppChatLogsParams;

    // Define start and end times for November 2024
    const startTime = new Date(Date.UTC(2024, 10, 1, 0, 0, 0)); // November 1st, 2024, 00:00:00
    const endTime = new Date(Date.UTC(2024, 10, 30, 23, 59, 59, 999)); // November 30th, 2024, 23:59:59.999

    const where = {
      $or: [{ userGoodFeedback: { $exists: true } }, { userBadFeedback: { $exists: true } }]
      // time: {
      //   $gte: startTime,
      //   $lte: endTime
      // }
    };

    const [data, total] = await Promise.all([
      MongoChatItem.aggregate([
        { $match: where },
        // { $sort: { time: -1 } },
        { $skip: (pageNum - 1) * pageSize },
        { $limit: pageSize },
        {
          $project: {
            chatId: 1,
            appId: 1,
            userGoodFeedback: 1,
            userBadFeedback: 1,
            query: '$responseData.query', // 直接提取 query 数组
            // time: 1,
            value: 1
          }
        },
        {
          $addFields: {
            // 如果 query 是数组，提取所有查询内容
            extractedQueries: {
              $map: {
                input: '$query',
                as: 'item',
                in: '$$item.query'
              }
            }
          }
        }
      ]),
      MongoChatItem.countDocuments(where)
    ]);

    jsonRes<PagingData<any>>(res, {
      data: {
        pageNum,
        pageSize,
        data,
        total
      }
    });
  } catch (error) {
    jsonRes(res, {
      code: 500,
      error
    });
  }
}
