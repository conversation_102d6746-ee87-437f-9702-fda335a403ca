import { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { authCert } from '@wiserag/service/support/permission/auth/common';
import { MongoPrompt } from '@wiserag/service/support/user/prompt';
import { connectToDatabase } from '@/service/mongo';

// 删除 prompt 接口
export default async function handler(req: NextApiRequest, res: NextApiResponse<any>) {
  try {
    await connectToDatabase();
    const { deleteId } = req.query as { deleteId: string };

    const deletedPrompt = await MongoPrompt.findByIdAndDelete(deleteId);
    jsonRes(res, {
      data: deletedPrompt
    });
  } catch (err) {
    jsonRes(res, {
      code: 500,
      error: err
    });
  }
}
