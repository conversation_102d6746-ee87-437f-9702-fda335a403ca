import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { authCert } from '@wiserag/service/support/permission/auth/common';
import { MongoUser } from '@wiserag/service/support/user/schema';
import { connectToDatabase } from '@/service/mongo';

import { ERROR_ENUM } from '@wiserag/global/common/error/errorCode';
import {
  TeamItemType,
  TeamMemberSchema,
  TeamMemberWithTeamSchema,
  TeamMemberWithUserSchema
} from '@wiserag/global/support/user/team/type';
import { TeamMemberRoleEnum, notLeaveStatus } from '@wiserag/global/support/user/team/constant';
import { MongoTeamMember } from '@wiserag/service/support/user/team/teamMemberSchema';
import { Types } from '@wiserag/service/common/mongo';

//获取当前用户为管理员(创建者)的团队列表信息
async function getTeamInfoByUserIdWithAdmin({ userId }: { userId: string }) {
  if (!userId) {
    return Promise.reject('userId is required');
  }
  return getTeamInfo({
    // 复用的构建的getTeamInfo代码
    userId: new Types.ObjectId(userId),
    status: notLeaveStatus,
    role: { $in: [TeamMemberRoleEnum.owner, TeamMemberRoleEnum.admin] }
  });
}
export default async function handler(req: NextApiRequest, res: NextApiResponse<any>) {
  try {
    await connectToDatabase();
    const { userId } = await authCert({ req, authToken: true });
    const groupTeamInfos = await getTeamInfoByUserIdWithAdmin({ userId: userId });

    jsonRes(res, {
      data: groupTeamInfos
    });
  } catch (err) {
    jsonRes(res, {
      code: 500,
      error: err
    });
  }
}
async function getTeamInfo(match: Record<string, any>): Promise<TeamItemType[]> {
  const tmbs = (await MongoTeamMember.find(match).populate('teamId')) as TeamMemberWithTeamSchema[];

  if (!tmbs) {
    return Promise.reject('member not exist');
  }

  return tmbs.map((tmb) => ({
    userId: String(tmb.userId),
    teamId: String(tmb.teamId._id),
    teamName: tmb.teamId.name,
    memberName: tmb.name,
    avatar: tmb.teamId.avatar,
    balance: tmb.teamId.balance,
    tmbId: String(tmb._id),
    role: tmb.role,
    status: tmb.status,
    defaultTeam: tmb.defaultTeam,
    canWrite: tmb.role !== TeamMemberRoleEnum.visitor,
    maxSize: tmb.teamId.maxSize
  }));
}
