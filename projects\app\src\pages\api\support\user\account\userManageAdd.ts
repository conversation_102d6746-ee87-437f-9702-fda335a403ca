import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { authCert } from '@wiserag/service/support/permission/auth/common';
import { MongoUser } from '@wiserag/service/support/user/schema';
import { connectToDatabase } from '@/service/mongo';
import { hashStr } from '@wiserag/global/common/string/tools';
import { createDefaultTeam } from '@wiserag/service/support/user/team/controller';
import { PRICE_SCALE } from '@wiserag/global/support/wallet/bill/constants';
import { mongoSessionRun } from '@wiserag/service/common/mongo/sessionRun';
// 新增用户接口
async function UserAdd(name: any, phone: any, rootUser: any) {
  // const rootUser = await MongoUser.findOne({
  //   visualName: name
  // });
  // console.log("🚀 ~ UserAdd ~ rootUser:", rootUser)
  const psw = process.env.DEFAULT_ROOT_PSW || '123456';

  let rootId = rootUser?._id || '';

  // init root team
  await mongoSessionRun(async (session) => {
    // init root user
    // if (rootUser) {
    //   return jsonRes(res, {
    //     code: 400,
    //     error: '用户已存在' // 提示用户已存在
    //   });
    // } else {
    const [{ _id }] = await MongoUser.create(
      [
        {
          username: phone,
          password: hashStr(psw),
          visualName: name
        }
      ],
      { session }
    );
    rootId = _id;
    // }
    // init root team
    // 创建团队
    await createDefaultTeam({ userId: rootId, maxSize: 1, balance: 9999 * PRICE_SCALE, session });
  });
}
export default async function handler(req: NextApiRequest, res: NextApiResponse<any>) {
  try {
    await connectToDatabase();
    const { name, phone } = req.body as { name: string; phone: string };
    const rootUser = await MongoUser.findOne({
      $or: [{ username: phone }, { visualName: name }]
    });
    console.log('🚀 ~ UserAdd ~ rootUser:', rootUser);
    if (rootUser) {
      return jsonRes(res, {
        code: 400,
        error: '用户已存在' // 提示用户已存在
      });
    } else {
      const user = await UserAdd(name, phone, rootUser);
      jsonRes(res, {
        data: {
          user
        }
      });
    }
  } catch (err) {
    jsonRes(res, {
      code: 500,
      error: err
    });
  }
}
