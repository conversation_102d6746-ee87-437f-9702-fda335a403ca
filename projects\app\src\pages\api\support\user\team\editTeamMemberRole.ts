import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { authCert } from '@wiserag/service/support/permission/auth/common';
import { MongoUser } from '@wiserag/service/support/user/schema';
import { connectToDatabase } from '@/service/mongo';

import { ERROR_ENUM } from '@wiserag/global/common/error/errorCode';
import { TeamMemberSchema, TeamMemberWithUserSchema } from '@wiserag/global/support/user/team/type';
import {
  TeamMemberRoleEnum,
  TeamMemberStatusEnum,
  notLeaveStatus
} from '@wiserag/global/support/user/team/constant';
import { MongoTeamMember } from '@wiserag/service/support/user/team/teamMemberSchema';
import { Types } from '@wiserag/service/common/mongo';

//改变role
export async function changeMemberRole(id: string, role: string): Promise<boolean> {
  try {
    // '=============changeMemberRole================'
    // 'changeMemberRole input id:', id
    // 根据id查找团队成员
    const tmb = await MongoTeamMember.findById(id);

    if (tmb) {
      // 成员存在，执行更新操作
      tmb.role = role as 'owner' | 'admin' | 'visitor'; // 更新role值
      await tmb.save(); // 保存更新后的成员数据
      return true; // 操作成功，返回true
    } else {
      return false; // 成员不存在，返回false
    }
  } catch (err) {
    throw new Error('Failed to change team member role: ' + err);
  }
}
export default async function handler(req: NextApiRequest, res: NextApiResponse<any>) {
  try {
    await connectToDatabase();
    const { id, role } = req.body as { id: string; role: string };

    const data = await changeMemberRole(id, role);

    jsonRes(res, {
      data: data
    });
  } catch (err) {
    jsonRes(res, {
      code: 500,
      error: err
    });
  }
}
