const express = require('express');
   const fs = require('fs');
   const path = require('path');
   const cors = require('cors'); // 引入 cors

   const app = express();
   app.use(cors()); // 使用 cors 中间件
   app.use(express.json());

   app.post('/api/update-config', (req, res) => {
     const { llmModels } = req.body;
     console.log("🚀 ~ app.post ~ llmModels:", llmModels);
     const configPath = path.join(__dirname, '../projects/app/data/config.json'); // 确保路径正确

     // 读取现有配置
     fs.readFile(configPath, 'utf8', (err, data) => {
       if (err) {
         return res.status(500).send('读取配置失败');
       }

       const config = JSON.parse(data);
       config.llmModels = llmModels; // 替换 llmModels

       fs.writeFile(configPath, JSON.stringify(config, null, 2), (err) => {
         if (err) {
           return res.status(500).send('更新配置失败');
         }
         res.send('配置更新成功');
       });
     });
   });

   app.listen(3000, () => {
     console.log('Server is running on port 3000');
   });