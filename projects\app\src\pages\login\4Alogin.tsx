import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Spinner, Text } from '@chakra-ui/react';
import { cookieUserMessage } from '@/web/support/user/api';
// import <PERSON>ie from 'js-cookie';
import { useUserStore } from '@/web/support/user/useUserStore';
import { useChatStore } from '@/web/core/chat/storeChat';
import { setToken } from '@/web/support/user/auth';
import { useRouter } from 'next/router';

const SimpleLoginPage = () => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const { setUserInfo } = useUserStore();
  const { setLastChatId, setLastChatAppId } = useChatStore();
  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        const { token } = router.query;
        console.log(1);
        if (token && typeof token === 'string') {
          const userMessage = await cookieUserMessage({ token });
          if (userMessage && 'user' in userMessage) {
            // 检查 userMessage 是否有 user 属性
            setLastChatId('');
            setLastChatAppId('');
            setUserInfo((userMessage as any).user);
            setToken(token);
            setTimeout(() => {
              router.push('/app/list');
            }, 300);
          } else {
            setIsLoading(false);
          }
        } else {
          setIsLoading(false);
        }
      } catch (error) {
        console.error('获取用户信息失败', error);
        setIsLoading(false);
      }
    };

    if (router.isReady) {
      fetchUserInfo();
    }
  }, [router.isReady, router.query]);

  return (
    <Flex
      alignItems="center"
      justifyContent="center"
      height="100vh"
      bg="gray.100"
      flexDirection="column"
    >
      {isLoading ? (
        <>
          <Spinner thickness="4px" speed="0.65s" emptyColor="gray.200" color="blue.500" size="xl" />
          <Text mt={4}>正在登录...</Text>
        </>
      ) : (
        <Text>登录失败，请刷新页面重试</Text>
      )}
    </Flex>
  );
};

export default SimpleLoginPage;
