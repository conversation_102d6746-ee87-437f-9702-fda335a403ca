import type { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { authCert } from '@wiserag/service/support/permission/auth/common';
import { MongoUser } from '@wiserag/service/support/user/schema';
import { connectToDatabase } from '@/service/mongo';

import { ERROR_ENUM } from '@wiserag/global/common/error/errorCode';
import { TeamItemType, TeamMemberWithTeamSchema } from '@wiserag/global/support/user/team/type';
import { TeamMemberRoleEnum } from '@wiserag/global/support/user/team/constant';
import { MongoTeamMember } from '@wiserag/service/support/user/team/teamMemberSchema';
import { MongoTeam } from '@wiserag/service/support/user/team/teamSchema';

//修改团队名称
async function updateTeamName(teamId: string, teamName: string) {
  if (!teamId) {
    return Promise.reject('teamId is required');
  }
  if (!teamName) {
    return Promise.reject('teamName is required');
  }

  try {
    const updatedTeam = await MongoTeam.findOneAndUpdate(
      { _id: Object(teamId) },
      { name: teamName },
      { new: true }
    );
    const Team = await MongoTeam.findOne({ _id: Object(teamId) });
    return updatedTeam;
  } catch (error) {
    return Promise.reject(error);
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse<any>) {
  try {
    await connectToDatabase();
    const { teamId, teamName } = req.body; // 从请求体中获取 teamId 和 teamName 的值

    // 调用 updateTeamName 函数来修改团队名称
    const updatedTeam = await updateTeamName(teamId, teamName);

    jsonRes(res, {
      data: updatedTeam
    });
  } catch (err) {
    jsonRes(res, {
      code: 500,
      error: err
    });
  }
}
