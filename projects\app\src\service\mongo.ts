import { startQueue } from './utils/tools';
import { PRICE_SCALE } from '@wiserag/global/support/wallet/bill/constants';
import { MongoUser } from '@wiserag/service/support/user/schema';
import { connectMongo } from '@wiserag/service/common/mongo/init';
import { hashStr } from '@wiserag/global/common/string/tools';
import { createDefaultTeam } from '@wiserag/service/support/user/team/controller';
import { exit } from 'process';
import { initVectorStore } from '@wiserag/service/common/vectorStore/controller';
import { getInitConfig } from '@/pages/api/common/system/getInitData';
import { startCron } from './common/system/cron';
import { mongoSessionRun } from '@wiserag/service/common/mongo/sessionRun';
import { LOG } from '@wiserag/service/core/loginfo/log';

/**
 * 函数 connectToDatabase 连接到 MongoDB 数据库，并在连接建立后执行各种初始化任务。
 *
 * @return `connectToDatabase` 函数返回一个解析为 void 的 Promise。
 */
export function connectToDatabase(): Promise<void> {
  return connectMongo({
    beforeHook: () => {},
    /* connectToDatabase 函数中的 afterHook 函数负责在与 MongoDB 数据库建立连接后执行各种初始化任务。以下是每项任务的详细说明： */
    afterHook: async () => {
      LOG('connectMongo:afterHook==>系统初始化', 'debug');
      // 初始化 向量库
      initVectorStore();
      // start queue 启动队列
      startQueue();
      // init system config
      getInitConfig();
      // cron 启动任务
      startCron();
      // 初始化用户
      initRootUser();
    }
  });
}
/**
 * 函数“initRootUser”初始化 MongoDB 数据库中的 root 用户，设置密码并在必要时创建默认团队。
 */

async function initRootUser() {
  try {
    const rootUser = await MongoUser.findOne({
      username: 'root'
    });
    const psw = process.env.DEFAULT_ROOT_PSW || '123456';

    let rootId = rootUser?._id || '';

    await mongoSessionRun(async (session) => {
      // init root user
      if (rootUser) {
        // change 如果用户存在不更新密码 jcb
        // await MongoUser.findOneAndUpdate(
        //   { username: 'root' },
        //   {
        //     password: hashStr(psw)
        //   }
        // );
      } else {
        const [{ _id }] = await MongoUser.create(
          [
            {
              username: 'root',
              password: hashStr(psw)
            }
          ],
          { session }
        );
        rootId = _id;
      }
      // init root team
      // 创建团队
      await createDefaultTeam({ userId: rootId, maxSize: 1, balance: 9999 * PRICE_SCALE, session });
    });

    console.log(`root user init:`, {
      username: 'root',
      password: psw
    });
  } catch (error) {
    console.log('init root user error', error);
    exit(1);
  }
}
