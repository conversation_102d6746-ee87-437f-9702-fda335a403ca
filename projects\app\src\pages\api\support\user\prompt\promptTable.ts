import { NextApiRequest, NextApiResponse } from 'next';
import { jsonRes } from '@wiserag/service/common/response';
import { authCert } from '@wiserag/service/support/permission/auth/common';
import { MongoPrompt } from '@wiserag/service/support/user/prompt';
import { connectToDatabase } from '@/service/mongo';

// 展示prompt字段接口
export default async function handler(req: NextApiRequest, res: NextApiResponse<any>) {
  try {
    await connectToDatabase();
    const { keyword, pageNum, pageSize } = req.body as {
      keyword: string;
      pageNum: number;
      pageSize: number;
    };

    const query = { title: { $regex: keyword, $options: 'i' } };
    const options = {
      skip: (pageNum - 1) * pageSize,
      limit: pageSize
    };

    const [promptFields, totalPromptFields] = await Promise.all([
      MongoPrompt.find(query, 'title desc aiChatQuoteTemplate aiChatQuotePrompt', options),
      MongoPrompt.countDocuments(query)
    ]);

    jsonRes(res, {
      // pageNum,
      // pageSize,
      // data: promptFields,
      // total: promptFields.length
      data: {
        data: promptFields,
        pageNum,
        pageSize,
        total: totalPromptFields
      }
    });
  } catch (err) {
    jsonRes(res, {
      code: 500,
      error: err
    });
  }
}
