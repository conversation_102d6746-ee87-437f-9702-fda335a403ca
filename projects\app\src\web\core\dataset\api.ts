import { GET, POST, PUT, DELETE } from '@/web/common/api/request';
import type { ParentTreePathItemType } from '@wiserag/global/common/parentFolder/type.d';
import type { DatasetItemType, DatasetListItemType } from '@wiserag/global/core/dataset/type.d';
import type {
  GetDatasetCollectionsProps,
  GetDatasetDataListProps,
  UpdateDatasetCollectionParams
} from '@/global/core/api/datasetReq.d';
import type {
  CreateDatasetCollectionParams,
  DatasetUpdateBody,
  LinkCreateDatasetCollectionParams,
  PostWebsiteSyncParams
} from '@wiserag/global/core/dataset/api.d';
import type {
  GetTrainingQueueProps,
  GetTrainingQueueResponse,
  SearchTestProps,
  SearchTestResponse
} from '@/global/core/dataset/api.d';
import type {
  UpdateDatasetDataProps,
  CreateDatasetParams,
  InsertOneDatasetDataProps
} from '@/global/core/dataset/api.d';
import type {
  PushDatasetDataProps,
  PushDatasetDataResponse
} from '@wiserag/global/core/dataset/api.d';
import type { DatasetCollectionItemType } from '@wiserag/global/core/dataset/type';
import {
  DatasetCollectionSyncResultEnum,
  DatasetTypeEnum
} from '@wiserag/global/core/dataset/constants';
import type { DatasetDataItemType } from '@wiserag/global/core/dataset/type';
import type { DatasetCollectionsListItemType } from '@/global/core/dataset/type.d';
import { PagingData } from '@/types';
import { ObjectId } from '@wiserag/service/common/mongo';

//pdfHighlight pdf高亮
export const getPdfHighlight = ({ bucketName, fileName }: { bucketName: any; fileName: any }) =>
  POST('/core/chat/pdfHighlight', {
    bucketName: bucketName,
    fileName: fileName
  });
/* ======================== dataset ======================= */
export const getDatasets = (data: { parentId?: string; type?: `${DatasetTypeEnum}` }) =>
  GET<DatasetListItemType[]>(`/core/dataset/list`, data);

export const publishDataApplication = ({ datasetId, userId }: { datasetId: any; userId: any }) =>
  POST('/core/dataset/publishDataApplication', {
    //发布接口
    datasetId: datasetId,
    userId: userId
  });
export const getDatasetIdByStore = (_id: string) =>
  POST('/core/dataset/getDatasetIdByStore', {
    //根据id在store表内查datasetId
    id: _id
  });
/**
 * get type=dataset list
 */
export const getAllDataset = () => GET<DatasetListItemType[]>(`/core/dataset/allDataset`);

export const getDatasetPaths = (parentId?: string) =>
  GET<ParentTreePathItemType[]>('/core/dataset/paths', { parentId });

export const getDatasetById = (id: string) => GET<DatasetItemType>(`/core/dataset/detail?id=${id}`);

export const postCreateDataset = (data: CreateDatasetParams) =>
  POST<string>(`/core/dataset/create`, data);

export const putDatasetById = (data: DatasetUpdateBody) => PUT<void>(`/core/dataset/update`, data);

export const delDatasetById = (id: string) => DELETE(`/core/dataset/delete?id=${id}`);

export const postWebsiteSync = (data: PostWebsiteSyncParams) =>
  POST(`/proApi/core/dataset/websiteSync`, data, {
    timeout: 600000
  }).catch();

//shopset  start---
//roardata_rag/projects/app/src/pages/api/core/shopset
export const getAllShopset = () => GET<DatasetListItemType[]>(`/core/shopset/allDataset`);

export const getShopsets = (data: { parentId?: string; type?: `${DatasetTypeEnum}` }) =>
  GET<DatasetListItemType[]>(`/core/shopset/list`, data);

export const getShopsetById = (id: string) => GET<DatasetItemType>(`/core/shopset/detail?id=${id}`);

export const putShopsetById = (data: DatasetUpdateBody) => PUT<void>(`/core/shopset/update`, data);

export const shopPostWebsiteSync = (data: PostWebsiteSyncParams) =>
  POST(`/proApi/core/shopset/websiteSync`, data, {
    timeout: 600000
  }).catch();
export const getShopsetPaths = (parentId?: string) =>
  GET<ParentTreePathItemType[]>('/core/shopset/paths', { parentId });

export const publishApplication = ({
  shareId,
  userId,
  datasetId,
  usedTime
}: {
  shareId: string;
  userId: string;
  datasetId: string;
  usedTime: any;
}) =>
  POST('/core/shopset/publishApplication', {
    //订阅接口
    shareId: shareId,
    userId: userId,
    datasetId: datasetId,
    usedTime: usedTime
  });
export const getDatasetIdByOutlinks = (_id: string, userId: string) =>
  POST('/core/shopset/getDatasetIdByOutlinks', {
    // 根据_id和userId 在outlinks表内查datasetId和userId
    _id: _id,
    userId: userId
  });
//shopset  end---

//appShopset  start---
//roardata_rag/projects/app/src/pages/api/core/appShopset
export const getAllappShopset = () => GET<DatasetListItemType[]>(`/core/appShopset/allDataset`);

export const getappShopsets = (data: { parentId?: string; type?: `${DatasetTypeEnum}` }) =>
  GET<DatasetListItemType[]>(`/core/appShopset/list`, data);

export const getappShopsetById = (id: string) =>
  GET<DatasetItemType>(`/core/appShopset/detail?id=${id}`);

export const putappShopsetById = (data: DatasetUpdateBody) =>
  PUT<void>(`/core/appShopset/update`, data);

export const appshopPostWebsiteSync = (data: PostWebsiteSyncParams) =>
  POST(`/proApi/core/appShopset/websiteSync`, data, {
    timeout: 600000
  }).catch();
export const getappShopsetPaths = (parentId?: string) =>
  GET<ParentTreePathItemType[]>('/core/appShopset/paths', { parentId });

export const appShoppublishApplication = ({
  shareId,
  userId,
  datasetId,
  usedTime
}: {
  shareId: string;
  userId: string;
  datasetId: string;
  usedTime: any;
}) =>
  POST('/core/appShopset/publishApplication', {
    //订阅接口
    shareId: shareId,
    userId: userId,
    datasetId: datasetId,
    usedTime: usedTime
  });
// { _id, userId }: { _id: any; userId: any }
// _id:string,userId :string
// export const getDatasetIdByOutlinks = (_id: string, userId: string) =>
//   POST('/core/appShopset/getDatasetIdByOutlinks', {
//     // 根据_id和userId 在outlinks表内查datasetId和userId
//     _id: _id,
//     userId: userId
//   });
//appShopset  end---

/* =========== search test ============ */
export const postSearchText = (data: SearchTestProps) =>
  POST<SearchTestResponse>(`/core/dataset/searchTest`, data);

/* ============================= collections ==================================== */
export const getDatasetCollections = (data: GetDatasetCollectionsProps) =>
  POST<PagingData<DatasetCollectionsListItemType>>(`/core/dataset/collection/list`, data);
export const getDatasetCollectionPathById = (parentId: string) =>
  GET<ParentTreePathItemType[]>(`/core/dataset/collection/paths`, { parentId });
export const getDatasetCollectionById = (id: string) =>
  GET<DatasetCollectionItemType>(`/core/dataset/collection/detail`, { id });
export const postDatasetCollection = (data: CreateDatasetCollectionParams) =>
  POST<string>(`/core/dataset/collection/create`, data);
export const postCreateDatasetLinkCollection = (data: LinkCreateDatasetCollectionParams) =>
  POST<{ collectionId: string }>(`/core/dataset/collection/create/link`, data);

export const putDatasetCollectionById = (data: UpdateDatasetCollectionParams) =>
  POST(`/core/dataset/collection/update`, data);
export const delDatasetCollectionById = (params: { id: string }) =>
  DELETE(`/core/dataset/collection/delete`, params);
export const postLinkCollectionSync = (collectionId: string) =>
  POST<`${DatasetCollectionSyncResultEnum}`>(`/core/dataset/collection/sync/link`, {
    collectionId
  });

/* =============================== data ==================================== */
/* get dataset list */
export const getDatasetDataList = (data: GetDatasetDataListProps) =>
  POST(`/core/dataset/data/list`, data);

export const getDatasetDataItemById = (id: string) =>
  GET<DatasetDataItemType>(`/core/dataset/data/detail`, { id });

/**
 * push data to training queue
 */
export const postChunks2Dataset = (data: PushDatasetDataProps) =>
  POST<PushDatasetDataResponse>(`/core/dataset/data/pushData`, data);

/**
 * insert one data to dataset (immediately insert)
 */
export const postInsertData2Dataset = (data: InsertOneDatasetDataProps) =>
  POST<string>(`/core/dataset/data/insertData`, data);

/**
 * update one datasetData by id
 */
export const putDatasetDataById = (data: UpdateDatasetDataProps) =>
  PUT('/core/dataset/data/update', data);
/**
 * 删除一条知识库数据
 */
export const delOneDatasetDataById = (id: string) =>
  DELETE<string>(`/core/dataset/data/delete`, { id });

/* ================ training ==================== */
/* get length of system training queue */
export const getTrainingQueueLen = (data: GetTrainingQueueProps) =>
  GET<GetTrainingQueueResponse>(`/core/dataset/training/getQueueLen`, data);

/* ================== file ======================== */
export const getFileViewUrl = (fileId: string) =>
  GET<string>('/core/dataset/file/getPreviewUrl', { fileId });
