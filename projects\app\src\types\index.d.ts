import {
  AudioSpeechModelType,
  ChatModelItemType,
  FunctionModelItemType,
  LLMModelItemType,
  ReRankModelItemType,
  VectorModelItemType,
  WhisperModelType
} from '@wiserag/global/core/ai/model.d';
import { TrackEventName } from '@/constants/common';
import { AppSimpleEditConfigTemplateType } from '@wiserag/global/core/app/type';
import { WiseGPTFeConfigsType, SystemEnvType } from '@wiserag/global/common/system/types';
import { SubPlanType } from '@wiserag/global/support/wallet/sub/type';

export type PagingData<T> = {
  pageNum: number;
  pageSize: number;
  data: T[];
  total?: number;
};

export type RequestPaging = { pageNum: number; pageSize: number; [key]: any };

declare global {
  var feConfigs: WiseGPTFeConfigsType;
  var systemEnv: SystemEnvType;
  var systemInitd: boolean;
  var subPlans: SubPlanType | undefined;

  var qaQueueLen: number;
  var vectorQueueLen: number;

  var llmModels: LLMModelItemType[];
  var vectorModels: VectorModelItemType[];
  var audioSpeechModels: AudioSpeechModelType[];
  var whisperModel: WhisperModelType;
  var reRankModels: ReRankModelItemType[];

  var systemVersion: string;

  var simpleModeTemplates: AppSimpleEditConfigTemplateType[];

  interface Window {
    grecaptcha: any;
    QRCode: any;
    umami?: {
      track: (event: `${TrackEventName}`, data: any) => void;
    };
  }
}
