import React, { useEffect, useMemo, useState } from 'react';
import {
  Flex,
  useTheme,
  Box,
  Modal,
  ModalHeader,
  ModalContent,
  ModalOverlay,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  But<PERSON>,
  useDisclosure
} from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import { useSystemStore } from '@/web/common/system/useSystemStore';
import MyIcon from '@wiserag/web/components/common/Icon';
import Tag from '@/components/Tag';
import Avatar from '@/components/Avatar';
import ToolMenu from './ToolMenu';
import type { ChatItemType } from '@wiserag/global/core/chat/type';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';
import { chatContentReplaceBlock } from '@wiserag/global/core/chat/utils';
import { getQuestionGuide } from '@/web/core/app/api';
import Markdown from '@/components/Markdown';

const ChatHeader = ({
  history,
  appName,
  appAvatar,
  chatModels,
  appId,
  showHistory,
  onOpenSlider
}: {
  history: ChatItemType[];
  appName: string;
  appAvatar: string;
  chatModels?: string[];
  appId?: string;
  showHistory?: boolean;
  onOpenSlider: () => void;
}) => {
  const router = useRouter();
  const theme = useTheme();
  const { t } = useTranslation();
  const { isPc } = useSystemStore();
  const title = useMemo(
    () =>
      chatContentReplaceBlock(history[history.length - 2]?.value)?.slice(0, 8) ||
      appName ||
      t('core.chat.New Chat'),
    [appName, history]
  );

  const [questionGuide, setquestionGuide] = useState();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { data: questionGuideData, refetch: questionGuideRefetch } = useQuery(
    ['getQuestionGuide', appId],
    () => getQuestionGuide(appId),
    {
      enabled: false
    }
  );
  useEffect(() => {
    if (questionGuideData) {
      setquestionGuide(questionGuideData);
      // console.log('🚀 ~ QuestionGuideData:', questionGuideData);
    }
  }, [questionGuideData]);

  const onOpenQuestionGuide = () => {
    questionGuideRefetch();
    onOpen();
  };

  return (
    <Flex
      alignItems={'center'}
      px={[3, 5]}
      h={['46px', '60px']}
      borderBottom={theme.borders.sm}
      color={'myGray.900'}
    >
      {isPc ? (
        <>
          <Box mr={3} color={'myGray.1000'}>
            {title}
          </Box>
          <Tag>
            <MyIcon name={'history'} w={'14px'} />
            <Box ml={1}>
              {history.length === 0
                ? t('core.chat.New Chat')
                : t('core.chat.History Amount', { amount: history.length })}
            </Box>
          </Tag>
          {!!chatModels && chatModels.length > 0 && (
            <Tag ml={2} colorSchema={'green'}>
              <MyIcon name={'core/chat/chatModelTag'} w={'14px'} />
              <Box ml={1}>{chatModels.join(',')}</Box>
            </Tag>
          )}
          <Tag ml={2} onClick={onOpenQuestionGuide}>
            {/* <MyIcon name={'core/chat/chatModelTag'} w={'14px'} /> */}
            <Box ml={1}>提问指引</Box>
          </Tag>
          <Box flex={1} />
        </>
      ) : (
        <>
          {showHistory && (
            <MyIcon
              name={'menu'}
              w={'20px'}
              h={'20px'}
              color={'myGray.900'}
              onClick={onOpenSlider}
            />
          )}

          <Flex px={3} alignItems={'center'} flex={'1 0 0'} w={0} justifyContent={'center'}>
            <Avatar src={appAvatar} w={'16px'} />
            <Box
              ml={1}
              className="textEllipsis"
              onClick={() => {
                appId && router.push(`/app/detail?appId=${appId}`);
              }}
            >
              {appName}
            </Box>
          </Flex>
        </>
      )}
      <ToolMenu history={history} />
      <Modal isOpen={isOpen} onClose={onClose} size={'xl'}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>提问指引</ModalHeader>
          <ModalCloseButton />
          <ModalBody>{questionGuide && <Markdown source={questionGuide} />}</ModalBody>
          <ModalFooter>
            <Button colorScheme="blue" mr={3} onClick={onClose}>
              关闭
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Flex>
  );
};

export default ChatHeader;
